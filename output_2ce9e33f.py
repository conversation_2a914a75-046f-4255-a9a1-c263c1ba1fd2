from pyexcalidraw import ExcalidrawDocument, Rectangle, Arrow, Text
import argparse

def generate_diagram(args):
    doc = ExcalidrawDocument()

    # Define common colors and fonts
    color_ui = "#b1a7d6"  # Purple for UI
    color_input = "#b4d8c7"  # Green for data input
    color_core = "#a7c9d6"  # Blue for core model
    color_output = "#f0c29f"  # Orange for output/operation
    stroke_color = "#1e1e1e"
    font_size_module = 20
    font_size_title = 30

    # 1. Add Title
    title = Text(
        x=350,
        y=50,
        text="Qlib 架构图",
        fontSize=font_size_title,
        textAlign="center",
        verticalAlign="middle",
        width=200,
        height=40,
    )
    doc.add_element(title)

    # 2. Key Elements with specified positions and colors
    # Top Center: User Interface
    ui = Rectangle(
        x=350,
        y=120,
        width=200,
        height=70,
        backgroundColor=color_ui,
        strokeColor=stroke_color,
        text="用户界面\n(User Interface)",
        fontSize=font_size_module,
        roundness={"type": 3, "value": 10}, # Slightly rounded corners for UI
    )
    doc.add_element(ui)

    # Left Column: Data Input/Preparation
    data_management = Rectangle(
        x=100,
        y=350,
        width=180,
        height=70,
        backgroundColor=color_input,
        strokeColor=stroke_color,
        text="数据管理\n(Data Management)",
        fontSize=font_size_module,
    )
    doc.add_element(data_management)

    feature_engineering = Rectangle(
        x=100,
        y=500,
        width=180,
        height=70,
        backgroundColor=color_input,
        strokeColor=stroke_color,
        text="特征工程\n(Feature Engineering)",
        fontSize=font_size_module,
    )
    doc.add_element(feature_engineering)

    # Center Area: Core
    ai_model = Rectangle(
        x=350,
        y=450,
        width=200,
        height=80,
        backgroundColor=color_core,
        strokeColor=stroke_color,
        text="AI模型\n(AI Model)",
        fontSize=font_size_module,
    )
    doc.add_element(ai_model)

    # Right Column: Model Output/Operation
    strategy_backtesting = Rectangle(
        x=620,
        y=350,
        width=200,
        height=70,
        backgroundColor=color_output,
        strokeColor=stroke_color,
        text="策略回测\n(Strategy Backtesting)",
        fontSize=font_size_module,
    )
    doc.add_element(strategy_backtesting)
    
    risk_management = Rectangle(
        x=620,
        y=500,
        width=200,
        height=70,
        backgroundColor=color_output,
        strokeColor=stroke_color,
        text="风险管理\n(Risk Management)",
        fontSize=font_size_module,
    )
    doc.add_element(risk_management)

    trading_execution = Rectangle(
        x=620,
        y=650,
        width=200,
        height=70,
        backgroundColor=color_output,
        strokeColor=stroke_color,
        text="交易执行\n(Trading Execution)",
        fontSize=font_size_module,
    )
    doc.add_element(trading_execution)

    # 3. Connections (Arrows)

    # From User Interface to Other Modules (Control/Request Flow - Dashed Arrows)
    doc.add_element(Arrow(from_obj=ui, to_obj=data_management, strokeColor=stroke_color, strokeWidth=1, endArrowhead="arrow", strokeStyle="dashed", text="配置/查询"))
    doc.add_element(Arrow(from_obj=ui, to_obj=feature_engineering, strokeColor=stroke_color, strokeWidth=1, endArrowhead="arrow", strokeStyle="dashed", text="配置/请求"))
    doc.add_element(Arrow(from_obj=ui, to_obj=ai_model, strokeColor=stroke_color, strokeWidth=1, endArrowhead="arrow", strokeStyle="dashed", text="训练/预测请求"))
    doc.add_element(Arrow(from_obj=ui, to_obj=strategy_backtesting, strokeColor=stroke_color, strokeWidth=1, endArrowhead="arrow", strokeStyle="dashed", text="配置/启动"))
    doc.add_element(Arrow(from_obj=ui, to_obj=risk_management, strokeColor=stroke_color, strokeWidth=1, endArrowhead="arrow", strokeStyle="dashed", text="配置/监控"))
    doc.add_element(Arrow(from_obj=ui, to_obj=trading_execution, strokeColor=stroke_color, strokeWidth=1, endArrowhead="arrow", strokeStyle="dashed", text="控制/启动"))

    # Data Flow (Core Process - Solid Arrows)
    doc.add_element(Arrow(from_obj=data_management, to_obj=feature_engineering, strokeColor=stroke_color, strokeWidth=2, endArrowhead="arrow", text="数据传输"))
    doc.add_element(Arrow(from_obj=feature_engineering, to_obj=ai_model, strokeColor=stroke_color, strokeWidth=2, endArrowhead="arrow", text="特征数据输入"))
    doc.add_element(Arrow(from_obj=ai_model, to_obj=strategy_backtesting, strokeColor=stroke_color, strokeWidth=2, endArrowhead="arrow", text="模型预测结果"))
    doc.add_element(Arrow(from_obj=strategy_backtesting, to_obj=risk_management, strokeColor=stroke_color, strokeWidth=2, endArrowhead="arrow", text="回测结果/风险暴露"))
    doc.add_element(Arrow(from_obj=strategy_backtesting, to_obj=trading_execution, strokeColor=stroke_color, strokeWidth=2, endArrowhead="arrow", text="策略指令"))
    doc.add_element(Arrow(from_obj=ai_model, to_obj=risk_management, strokeColor=stroke_color, strokeWidth=2, endArrowhead="arrow", text="模型风险数据"))
    doc.add_element(Arrow(from_obj=risk_management, to_obj=trading_execution, strokeColor=stroke_color, strokeWidth=2, endArrowhead="arrow", text="风险控制/限额"))

    # Feedback/Monitoring Flow (Dashed Arrows)
    doc.add_element(Arrow(from_obj=strategy_backtesting, to_obj=ui, strokeColor=stroke_color, strokeWidth=1, endArrowhead="arrow", startArrowhead=None, strokeStyle="dashed", text="回测结果展示"))
    doc.add_element(Arrow(from_obj=risk_management, to_obj=ui, strokeColor=stroke_color, strokeWidth=1, endArrowhead="arrow", startArrowhead=None, strokeStyle="dashed", text="风险状态展示"))
    doc.add_element(Arrow(from_obj=trading_execution, to_obj=ui, strokeColor=stroke_color, strokeWidth=1, endArrowhead="arrow", startArrowhead=None, strokeStyle="dashed", text="交易状态/结果展示"))
    doc.add_element(Arrow(from_obj=ai_model, to_obj=ui, strokeColor=stroke_color, strokeWidth=1, endArrowhead="arrow", startArrowhead=None, strokeStyle="dashed", text="训练/性能展示"))
    doc.add_element(Arrow(from_obj=data_management, to_obj=ui, strokeColor=stroke_color, strokeWidth=1, endArrowhead="arrow", startArrowhead=None, strokeStyle="dashed", text="数据状态/元数据"))

    # Save document
    doc.save(args.output)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Generate an Excalidraw diagram for Qlib Architecture.")
    parser.add_argument("--output", type=str, default="qlib_architecture.excalidraw",
                        help="The filename for the output Excalidraw JSON file.")

    args = parser.parse_args()
    generate_diagram(args)