#!/usr/bin/env python3
import argparse
import concurrent.futures
import json
import os
import subprocess
import sys
from pathlib import Path
from typing import Any, Optional

# Add project root to path
project_root = Path(__file__).resolve().parent
sys.path.append(str(project_root))

from loguru import logger

from agents.dsl_generation_agent import DSLGenerationToolkit


class StoryboardProcessor:
    """
    处理分镜脚本并转换为视频的处理器。

    功能:
    1. 解析分镜脚本JSON文件
    2. 为每个分镜条目生成DSL JSON文件
    3. 运行DSL到Manim的转换和渲染
    4. 支持分镜条目的并行处理
    """

    def __init__(
        self,
        storyboard_file: str,
        output_dir: str = "output",
        dsl_schema_file: str = "docs/animation_functions.md",
        max_workers: int = 4,
        quality: str = "l",
        stages_str: str = "dsl,code,render,subtitles,concat",
        project_name: str = None,
    ):
        """
        初始化StoryboardProcessor。

        参数:
            storyboard_file: 分镜脚本JSON文件路径
            output_dir: 输出文件存储目录
            dsl_schema_file: DSL模式文件路径
            max_workers: 并行处理的最大工作线程数
            quality: Manim渲染质量 ('l', 'm', 'h', 'k')
            stages_str: 逗号分隔的阶段列表
            project_name: 项目名称，用于构建最终视频文件名
        """
        self.storyboard_file = storyboard_file
        self.output_dir = output_dir
        self.dsl_schema_file = dsl_schema_file
        self.max_workers = max_workers
        self.quality = quality
        self.project_name = project_name
        # Parse and store stages
        self.stages_to_run_globally = [s.strip() for s in stages_str.split(",") if s.strip()]

        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)

        # Load DSL schema and actions documentation
        self.dsl_schema = self._load_dsl_documentation()

        # Initialize the DSL generation toolkit
        self.dsl_toolkit = DSLGenerationToolkit()

    def _load_dsl_documentation(self) -> str:
        with open(self.dsl_schema_file, encoding="utf-8") as f:
            schema = f.read()

        return schema

    def load_storyboard(self) -> list[dict[str, Any]]:
        """
        Load and parse the storyboard JSON file.

        Returns:
            List of storyboard entries
        """
        try:
            with open(self.storyboard_file, encoding="utf-8") as f:
                data = json.load(f)

                if isinstance(data, dict):
                    # If the root is a dict, look for a 'storyboard' key
                    storyboard = data.get("storyboard", [])
                else:
                    # Assume it's already a list of storyboard entries
                    storyboard = data

                logger.info(f"Loaded storyboard with {len(storyboard)} entries")
                return storyboard
        except Exception as e:
            logger.error(f"Error loading storyboard file: {e}")
            return []

    def _find_rendered_video(self, idx: int, attempt_suffix: str, scene_name_for_file: Optional[str]) -> Optional[str]:
        """
        Find a rendered video file based on naming conventions.
        Manim's output structure is typically: media/videos/<py_file_name_stem>/<quality>/<scene_name>.mp4
        The <py_file_name_stem> corresponds to the Python file that contained the Manim scene.
        The SRT file is also typically <scene_name>.srt in the same directory.
        """
        # This method will be more fully implemented later.
        # For now, it needs to correctly derive paths based on how Manim outputs files.
        # The current logic in _add_subtitles is a starting point but needs generalization.
        logger.debug(
            f"Placeholder for _find_rendered_video called with idx={idx}, suffix={attempt_suffix}, scene_name={scene_name_for_file}"
        )

        # Simplified logic from _add_subtitles for now, will need refinement
        seq_num = idx + 1
        project_root = Path(__file__).resolve().parent

        video_file_basename = f"{scene_name_for_file}.mp4" if scene_name_for_file else f"Storyboard_{seq_num}.mp4"

        py_file_abs_path = (
            Path(self.output_dir) / "generated_manim_code" / f"storyboard_{seq_num}{attempt_suffix}_dsl.py"
        )

        # Simplified py_file_rel_path_stem based on observation that Manim uses the .py filename directly for its media subdir.
        py_filename_stem = py_file_abs_path.stem  # e.g., "storyboard_1_attempt_1_dsl"
        video_base_dir_for_manim_output = project_root / "media" / "videos" / py_filename_stem

        quality_char_to_folder = {"l": "480p15", "m": "720p30", "h": "1080p60", "p": "1440p60", "k": "2160p60"}
        # Use self.quality to determine the expected folder
        expected_res_folder = quality_char_to_folder.get(self.quality, "480p15")  # Default to 'l' if quality is unknown

        # We should primarily look in the folder corresponding to self.quality
        # For now, let's iterate as a fallback or if the mapping isn't perfect.
        # Correct approach: only check the folder for self.quality.

        current_video_dir = video_base_dir_for_manim_output / expected_res_folder
        current_input_video_path = current_video_dir / video_file_basename

        if current_input_video_path.exists():
            logger.info(
                f"Found video for entry {seq_num}, suffix {attempt_suffix}, scene {scene_name_for_file} at {current_input_video_path}"
            )
            return str(current_input_video_path)

        logger.warning(
            f"Video not found for entry {seq_num}, suffix {attempt_suffix}, scene {scene_name_for_file} with expected basename {video_file_basename} in dir pattern {video_base_dir_for_manim_output}/<quality_folder>/"
        )
        return None

    def _add_subtitles(self, idx: int, attempt_suffix: str, scene_name_for_files: Optional[str]) -> Optional[str]:
        """
        Add subtitles to a rendered video file using ffmpeg.

        Assumes the rendered video and corresponding SRT file exist in the expected
        media directory structure.

        Args:
            idx: The 0-based index of the storyboard entry.
            attempt_suffix: The suffix string for the specific attempt (e.g., "_attempt_1").
            scene_name_for_files: The specific scene name used for .mp4 and .srt files.

        Returns:
            Path to the subtitled video file, or None if subtitles could not be added
            (e.g., SRT file missing). Returns the original video path if SRT is missing.
            Raises FileNotFoundError if the base video is not found.
            Raises subprocess.CalledProcessError if ffmpeg fails.
        """
        logger.info(f"[Entry {idx+1}, Suffix {attempt_suffix}] Adding subtitles...")
        seq_num = idx + 1
        project_root = Path(__file__).resolve().parent

        video_file_basename = f"{scene_name_for_files}.mp4" if scene_name_for_files else f"Storyboard_{seq_num}.mp4"
        srt_file_basename = f"{scene_name_for_files}.srt" if scene_name_for_files else f"Storyboard_{seq_num}.srt"

        # Manim output directory structure based on quality
        quality_char_to_folder = {"l": "480p15", "m": "720p30", "h": "1080p60", "p": "1440p60", "k": "2160p60"}
        expected_res_folder = quality_char_to_folder.get(self.quality, "480p15")

        # Base directory where Manim outputs videos for this specific .py file stem
        # Corrected Manim output path logic (similar to _find_rendered_video)
        py_file_abs_path = (
            Path(self.output_dir) / "generated_manim_code" / f"storyboard_{seq_num}{attempt_suffix}_dsl.py"
        )
        # try:
        #     py_file_rel_path_stem = py_file_abs_path.relative_to(project_root).with_suffix('')
        # except ValueError:
        #     logger.warning(f"_add_subtitles: Could not make .py path {py_file_abs_path} relative to project root {project_root}. Using simplified stem.")
        #     py_file_rel_path_stem = Path(f"storyboard_{seq_num}{attempt_suffix}_dsl")

        # Simplified py_file_rel_path_stem
        py_filename_stem = py_file_abs_path.stem  # e.g., "storyboard_1_attempt_1_dsl"
        manim_py_file_specific_output_dir = project_root / "media" / "videos" / py_filename_stem

        video_dir = manim_py_file_specific_output_dir / expected_res_folder
        input_video_path = video_dir / video_file_basename
        srt_path = video_dir / srt_file_basename
        output_subtitled_video_path = video_dir / f"{Path(video_file_basename).stem}_subtitle.mp4"

        if not input_video_path.exists():
            logger.error(
                f"Rendered video not found for entry {seq_num}, suffix {attempt_suffix}, scene {scene_name_for_files} at {input_video_path}"
            )
            # Try searching with _find_rendered_video as a fallback, though ideally direct path construction works
            found_path_alt = self._find_rendered_video(idx, attempt_suffix, scene_name_for_files)
            if found_path_alt:
                logger.warning(
                    f"Fallback: Found video via _find_rendered_video at {found_path_alt}. Input path was {input_video_path}"
                )
                input_video_path = Path(found_path_alt)
                # Re-evaluate srt_path and output_subtitled_video_path based on this new input_video_path
                video_dir = input_video_path.parent
                srt_path = video_dir / srt_file_basename  # srt expected in same dir
                output_subtitled_video_path = video_dir / f"{input_video_path.stem}_subtitle.mp4"
            else:
                raise FileNotFoundError(f"Rendered video not found for entry {seq_num} (path: {input_video_path})")

        if not srt_path.exists():
            logger.warning(f"SRT file not found at {srt_path}. Skipping subtitle step, returning original video path.")
            return str(input_video_path)

        srt_filter_path = str(srt_path.resolve()).replace("\\", "/")

        ffmpeg_cmd = [
            "ffmpeg",
            "-i",
            str(input_video_path.resolve()),
            "-lavfi",
            f"subtitles='{srt_filter_path}':force_style='Alignment=2,MarginV=6,fontcolor=white,Fontsize=16,fontweight=bold,FontName=微软雅黑'",
            "-y",
            str(output_subtitled_video_path.resolve()),
        ]
        logger.debug(f"Running ffmpeg command: {' '.join(ffmpeg_cmd)}")

        try:
            subprocess.run(ffmpeg_cmd, check=True, capture_output=True, text=True, encoding="utf-8")
            logger.success(
                f"[Entry {seq_num}, Suffix {attempt_suffix}] Subtitles added successfully to {output_subtitled_video_path}"
            )
            return str(output_subtitled_video_path)
        except subprocess.CalledProcessError as sub_cpe:
            logger.error(f"[Entry {seq_num}, Suffix {attempt_suffix}] Failed to add subtitles. Error: {sub_cpe.stderr}")
            raise  # Re-raise the error to be caught by the processing loop

    def _process_single_entry_stages(
        self,
        idx: int,
        entry: dict[str, Any],
        stages_to_run: list[str],
        attempt_suffix: str,
        error_context_for_dsl: Optional[str],
    ) -> dict[str, Optional[str]]:
        """
        Processes a single storyboard entry through specified stages.
        Manages file paths for DSL, Python code, scene name, rendered video, and subtitled video.
        """
        output_paths: dict[str, Optional[str]] = {
            "dsl_file": None,
            "py_file": None,
            "scene_name": None,
            "rendered_video_file": None,
            "subtitled_video_file": None,
        }
        seq_num = idx + 1

        # --- Stage: DSL Generation ---
        # Determine DSL file path (used by 'dsl' stage and potentially 'code' if 'dsl' is skipped)
        dsl_filename = os.path.join(self.output_dir, f"storyboard_{seq_num}{attempt_suffix}_dsl.json")
        output_paths["dsl_file"] = str(Path(dsl_filename).resolve())  # Store absolute path

        if "dsl" in stages_to_run:
            logger.info(
                f"[Entry {seq_num}, Attempt {attempt_suffix}] Stage: DSL - Generating DSL for: {entry.get('分镜名', '')}"
            )
            if error_context_for_dsl:
                logger.warning(
                    f"[Entry {seq_num}, Attempt {attempt_suffix}] Retrying DSL generation with error context: {error_context_for_dsl}"
                )

            entry_str = json.dumps(entry, ensure_ascii=False, indent=2)
            generated_dsl_file = self.dsl_toolkit.generate_dsl_json(
                storyboard_content=entry_str,
                output_file=dsl_filename,  # generate_dsl_json should handle making this absolute or ensure it's relative to a known base
                dsl_schema=self.dsl_schema,
                seq_num=seq_num,
                error_context=error_context_for_dsl,
            )
            if not generated_dsl_file or not Path(generated_dsl_file).exists():
                raise ValueError(
                    f"DSL generation failed or returned an invalid/non-existent file path: {generated_dsl_file} for entry {seq_num}"
                )
            output_paths["dsl_file"] = str(Path(generated_dsl_file).resolve())  # Ensure absolute path from toolkit
            logger.success(f"[Entry {seq_num}] Stage: DSL - DSL file generated: {output_paths['dsl_file']}")
        elif not Path(output_paths["dsl_file"]).exists() and (
            "code" in stages_to_run or "render" in stages_to_run or "subtitles" in stages_to_run
        ):
            # If DSL is not run, but subsequent stages need it, it must exist.
            raise FileNotFoundError(
                f"DSL file {output_paths['dsl_file']} not found but required for subsequent stages and 'dsl' stage was not run."
            )

        # --- Stage: Manim Code Generation ---
        # Determine .py file path (used by 'code' stage and potentially 'render' if 'code' is skipped)
        # Store .py files in a subdirectory of self.output_dir for organization
        py_file_dir = Path(self.output_dir) / "generated_manim_code"
        os.makedirs(py_file_dir, exist_ok=True)
        # Use the dsl_file's stem for the py_file's stem to maintain linkage.
        # e.g., storyboard_1_attempt_1_dsl.json -> storyboard_1_attempt_1_dsl.py
        py_filename = (
            f"{Path(output_paths['dsl_file']).stem}.py"
            if output_paths["dsl_file"]
            else f"storyboard_{seq_num}{attempt_suffix}_generated.py"
        )
        output_py_path = py_file_dir / py_filename
        output_paths["py_file"] = str(output_py_path.resolve())

        # Determine scene name (used by 'code' to pass as suffix, and by 'render')
        # This logic needs to be robust and consistent with dsl_to_manim.py's scene naming
        final_scene_name = f"DefaultScene_Entry{seq_num}"  # Fallback
        base_scene_name_for_files = f"Storyboard_{seq_num}"  # Default base name for files

        if output_paths["dsl_file"] and Path(output_paths["dsl_file"]).exists():
            try:
                dsl_content_text = Path(output_paths["dsl_file"]).read_text(encoding="utf-8")
                dsl_content = json.loads(dsl_content_text)
                # Scene name from DSL title - this is the BASE name Manim will use for the class if suffix doesn't alter class name
                current_base_scene_name = (
                    dsl_content.get("metadata", {})
                    .get("title", f"Storyboard_{seq_num}")
                    .replace(" ", "_")
                    .replace("-", "_")
                )
                current_base_scene_name = "".join(
                    c if c.isalnum() or c == "_" else "_" for c in current_base_scene_name
                )  # Sanitize
                if not current_base_scene_name or (
                    not current_base_scene_name[0].isalpha() and current_base_scene_name[0] != "_"
                ):
                    current_base_scene_name = f"ManimScene_{current_base_scene_name}"
                base_scene_name_for_files = current_base_scene_name  # THIS is the name for .mp4 and .srt files

                # final_scene_name is the name we TELL manim to render (might include suffix if dsl_to_manim used it for class)
                # Since we confirmed class name IS NOT suffixed, final_scene_name for render call should be base_scene_name_for_files.
                final_scene_name = base_scene_name_for_files

            except Exception as e:
                logger.warning(
                    f"Could not derive scene name from DSL metadata for entry {seq_num}: {e}. Using fallback: {final_scene_name} and base: {base_scene_name_for_files}"
                )

        output_paths["scene_name"] = final_scene_name  # This will be used for manim render command, e.g. Storyboard_1
        output_paths[
            "base_scene_name_for_files"
        ] = base_scene_name_for_files  # This is for finding .mp4/.srt files, e.g. Storyboard_1

        # The suffix for dsl_to_manim --suffix. Since it doesn't affect class name, its utility is minor (e.g. for logs in dsl_to_manim)
        scene_suffix_for_dsl_to_manim_script = attempt_suffix.replace("_attempt_", "A") if attempt_suffix else ""

        if "code" in stages_to_run:
            logger.info(
                f"[Entry {seq_num}, Attempt {attempt_suffix}] Stage: CODE - Generating Manim Python code from DSL: {output_paths['dsl_file']}"
            )
            if not output_paths["dsl_file"] or not Path(output_paths["dsl_file"]).exists():
                raise ValueError(
                    f"DSL file path is missing or file does not exist for code generation stage for entry {seq_num}."
                )

            cmd = [
                "uv",
                "run",
                "python",
                "-m",
                "dsl.v2.dsl_to_manim",
                output_paths["dsl_file"],
                "--output",
                output_paths["py_file"],
                "--suffix",
                scene_suffix_for_dsl_to_manim_script,  # dsl_to_manim appends this to its derived base scene name in its logs/return
            ]
            logger.debug(f"Running Manim code generation command: {' '.join(cmd)}")
            try:
                code_gen_result = subprocess.run(cmd, check=True, capture_output=True, text=True, encoding="utf-8")
                logger.debug(f"Manim code generation stdout: {code_gen_result.stdout}")
                if code_gen_result.stderr:
                    logger.warning(f"Manim code generation stderr: {code_gen_result.stderr}")
            except subprocess.CalledProcessError as cpe:
                logger.error(f"Manim code generation failed for entry {seq_num}. Stderr: {cpe.stderr}")
                raise  # Re-raise to be caught by the processing loop's retry mechanism

            if not Path(output_paths["py_file"]).exists():
                raise FileNotFoundError(
                    f"Manim .py file {output_paths['py_file']} was not generated by dsl_to_manim for entry {seq_num}."
                )
            logger.success(
                f"[Entry {seq_num}] Stage: CODE - Manim Python code generated: {output_paths['py_file']} for scene: {output_paths['scene_name']}"
            )

        elif not Path(output_paths["py_file"]).exists() and ("render" in stages_to_run or "subtitles" in stages_to_run):
            raise FileNotFoundError(
                f"Manim .py file {output_paths['py_file']} not found but required for subsequent stages and 'code' stage was not run for entry {seq_num}."
            )

        # --- Stage: Video Rendering ---
        if "render" in stages_to_run:
            logger.info(
                f"[Entry {seq_num}, Attempt {attempt_suffix}] Stage: RENDER - Rendering video from .py: {output_paths['py_file']} (Scene: {output_paths['scene_name']})"
            )
            if not output_paths["py_file"] or not Path(output_paths["py_file"]).exists():
                raise ValueError(
                    f"Manim .py file path is missing or file does not exist for render stage for entry {seq_num}."
                )
            if not output_paths["scene_name"]:
                raise ValueError(f"Scene name is missing for render stage for entry {seq_num}.")

            # Command to render the specific scene from the generated .py file
            # Manim's output directory will be relative to where it's run (project root)
            # e.g., media/videos/<py_file_name_stem_relative_to_cwd>/<quality>/<scene_name>.mp4
            manim_cmd = [
                "uv",
                "run",
                "manim",
                "render",
                output_paths["py_file"],  # Path to the python file
                output_paths["scene_name"],  # Name of the scene class to render
                f"-q{self.quality}",  # Quality flag
            ]
            logger.debug(f"Running Manim render command: {' '.join(manim_cmd)}")
            try:
                render_result = subprocess.run(manim_cmd, check=True, capture_output=True, text=True, encoding="utf-8")
                logger.debug(f"Manim render stdout: {render_result.stdout}")
                if render_result.stderr:
                    logger.warning(f"Manim render stderr: {render_result.stderr}")
            except subprocess.CalledProcessError as cpe:
                logger.error(
                    f"Manim rendering failed for entry {seq_num}, scene {output_paths['scene_name']}. Stderr: {cpe.stderr}"
                )
                raise

            # Verify video output
            rendered_video_path = self._find_rendered_video(
                idx, attempt_suffix, output_paths["base_scene_name_for_files"]
            )
            if not rendered_video_path:
                # Log additional info to help debug why _find_rendered_video might fail
                logger.error(
                    f"Manim execution seemed to succeed for entry {seq_num} but rendered video was not found by _find_rendered_video."
                )
                logger.error(
                    f"Searched for base scene '{output_paths['base_scene_name_for_files']}' with suffix '{attempt_suffix}'. Check Manim output paths and naming conventions."
                )
                raise FileNotFoundError(
                    f"Rendered video not found after Manim execution for entry {seq_num}, scene {output_paths['base_scene_name_for_files']}."
                )
            output_paths["rendered_video_file"] = rendered_video_path
            logger.success(f"[Entry {seq_num}] Stage: RENDER - Video rendered: {output_paths['rendered_video_file']}")

        elif not output_paths.get("rendered_video_file") and "subtitles" in stages_to_run:
            # If render is not run, but subtitles stage needs it, try to find existing video
            logger.info(
                f"[Entry {seq_num}] 'render' stage skipped, attempting to find existing video for 'subtitles' stage."
            )
            found_video = self._find_rendered_video(idx, attempt_suffix, output_paths["base_scene_name_for_files"])
            if not found_video:
                raise FileNotFoundError(
                    f"Rendered video not found for entry {seq_num} (scene: {output_paths['base_scene_name_for_files']}) but required for 'subtitles' stage, and 'render' stage was not run."
                )
            output_paths["rendered_video_file"] = found_video
            logger.info(f"[Entry {seq_num}] Found existing video for 'subtitles': {found_video}")

        # --- Stage: Subtitle Addition ---
        if "subtitles" in stages_to_run:
            logger.info(
                f"[Entry {seq_num}, Attempt {attempt_suffix}] Stage: SUBTITLES - Adding subtitles to video: {output_paths['rendered_video_file']}"
            )
            if not output_paths["rendered_video_file"] or not Path(output_paths["rendered_video_file"]).exists():
                raise ValueError(
                    f"Rendered video file path is missing or file does not exist for subtitles stage for entry {seq_num}."
                )

            try:
                subtitled_video_path = self._add_subtitles(
                    idx, attempt_suffix, output_paths["base_scene_name_for_files"]
                )
                if (
                    subtitled_video_path == output_paths["rendered_video_file"]
                    and not Path(subtitled_video_path.replace(".mp4", "_subtitle.mp4")).exists()
                ):  # Check if it returned original due to missing SRT
                    logger.warning(
                        f"[Entry {seq_num}] Subtitle addition did not create a new file (likely missing SRT). Using original: {subtitled_video_path}"
                    )
                output_paths[
                    "subtitled_video_file"
                ] = subtitled_video_path  # _add_subtitles returns original if srt missing, or new if successful
                logger.success(
                    f"[Entry {seq_num}] Stage: SUBTITLES - Subtitle processing complete. Final video: {output_paths['subtitled_video_file']}"
                )
            except FileNotFoundError as fnfe:  # Specific for video not found within _add_subtitles
                logger.error(
                    f"[Entry {seq_num}] Stage: SUBTITLES - Prerequisite video file not found by _add_subtitles: {fnfe}"
                )
                raise
            except subprocess.CalledProcessError as cpe:  # Ffmpeg error
                logger.error(f"[Entry {seq_num}] Stage: SUBTITLES - ffmpeg failed. Stderr: {cpe.stderr}")
                raise
            except Exception as e:
                logger.error(f"[Entry {seq_num}] Stage: SUBTITLES - Error during subtitle addition: {e}")
                raise

        return output_paths

    def process_storyboard_entry_wrapper(
        self, idx: int, entry: dict[str, Any], stages_for_entry: list[str], max_retries: int = 2
    ) -> Optional[dict[str, Optional[str]]]:
        """
        Process a single storyboard entry with retries on failure, executing specified stages.

        Args:
            idx: The index of the storyboard entry
            entry: The storyboard entry data
            stages_for_entry: List of stages to run for this entry
            max_retries: Maximum number of retries allowed

        Returns:
            A dictionary containing paths to generated files for each successful stage, or None if processing failed after retries.
        """
        last_error = None
        error_context = None  # Store error context for retry attempts

        for attempt in range(max_retries + 1):
            attempt_suffix = f"_attempt_{attempt+1}"
            try:
                # Generate a unique name for the DSL file using the suffix
                # dsl_filename = os.path.join(self.output_dir, f"storyboard_{idx+1}{attempt_suffix}_dsl.json")

                # Log current stage list for clarity
                logger.info(
                    f"[Entry {idx+1}, Attempt {attempt+1}/{max_retries+1}] Processing with stages: {', '.join(stages_for_entry)}"
                )

                # Call the new stage-aware processing method
                entry_output_paths = self._process_single_entry_stages(
                    idx=idx,
                    entry=entry,  # Pass original entry data for DSL gen if needed
                    stages_to_run=stages_for_entry,
                    attempt_suffix=attempt_suffix,
                    error_context_for_dsl=error_context,  # Pass previous error context for DSL stage
                )

                # The 'final' video product for this entry depends on the stages run.
                # If 'subtitles' was run and succeeded, that's the one. Else, if 'render' was run, that's it.
                final_video_for_entry = entry_output_paths.get("subtitled_video_file") or entry_output_paths.get(
                    "rendered_video_file"
                )

                if (
                    not stages_for_entry or final_video_for_entry
                ):  # If stages were run and a video resulted, or if no stages (noop)
                    logger.success(
                        f"[Entry {idx+1}, Attempt {attempt+1}] Successfully completed stages: {', '.join(stages_for_entry)}. Final video: {final_video_for_entry}"
                    )
                    return entry_output_paths  # Return all paths for this successful attempt
                elif not final_video_for_entry and any(s in stages_for_entry for s in ["render", "subtitles"]):
                    # This case implies render/subtitles were requested but didn't produce a video path.
                    # _process_single_entry_stages should ideally raise an error before this.
                    # But as a safeguard:
                    last_error = "Processing stages completed but no final video path was determined."
                    logger.error(f"[Entry {idx+1}, Attempt {attempt+1}] {last_error}")
                    # No specific error context to pass, _process_single_entry_stages should have raised.

                # If only dsl or code stages were run, there's no "video" to return, but it's a success for those stages.
                elif not any(s in stages_for_entry for s in ["render", "subtitles"]):
                    logger.success(
                        f"[Entry {idx+1}, Attempt {attempt+1}] Successfully completed non-rendering stages: {', '.join(stages_for_entry)}."
                    )
                    return entry_output_paths

            except subprocess.CalledProcessError as cpe:
                error_message = str(cpe.stderr)
                current_stage_error = "Unknown stage"
                # Try to infer stage from command or error message (this is heuristic)
                if "dsl_to_manim" in error_message or "dsl.v2.dsl_to_manim" in " ".join(cpe.cmd):
                    current_stage_error = "CODE (dsl_to_manim)"
                elif "manim render" in error_message or "manim" in " ".join(cpe.cmd):  # Check command for manim
                    current_stage_error = "RENDER (manim)"
                elif "ffmpeg" in error_message or "subtitles=" in error_message:  # Check command or common ffmpeg error
                    current_stage_error = "SUBTITLES (ffmpeg)"

                last_error = (
                    f"Stage {current_stage_error} failed with return code {cpe.returncode}. Stderr: {cpe.stderr}"
                )
                logger.error(f"[Entry {idx+1}, Attempt {attempt+1}] Error during {current_stage_error}: {last_error}")
                error_context = f"Previous attempt failed during {current_stage_error}. Error: {cpe.stderr}"

            except FileNotFoundError as fnfe:
                last_error = str(fnfe)
                logger.error(f"[Entry {idx+1}, Attempt {attempt+1}] File not found error: {last_error}")
                error_context = f"Previous attempt failed: {last_error}"

            except ValueError as ve:  # For issues like missing DSL path before code gen
                last_error = str(ve)
                logger.error(f"[Entry {idx+1}, Attempt {attempt+1}] Value error: {last_error}")
                error_context = f"Previous attempt failed: {last_error}"

            except Exception as e:
                last_error = str(e)
                logger.error(f"[Entry {idx+1}, Attempt {attempt+1}] Generic error processing storyboard entry: {e}")
                error_context = f"Previous attempt failed. Error: {last_error}"

            # If this was the last attempt, break the loop
            if attempt == max_retries:
                break
            else:
                logger.info(f"[Entry {idx+1}] Retrying ({attempt+2}/{max_retries+1})...")

        logger.error(f"[Entry {idx+1}] Failed to process after {max_retries + 1} attempts. Last error: {last_error}")
        return None

    def _concatenate_videos(self, video_paths: list[str]) -> Optional[str]:
        """
        Concatenate a list of video files using ffmpeg.

        Args:
            video_paths: A list of paths to the video files to concatenate, in order.

        Returns:
            The path to the final concatenated video file, or None if concatenation fails.
        """
        if not video_paths:
            logger.warning("No video paths provided for concatenation.")
            return None

        logger.info(f"Concatenating {len(video_paths)} videos...")
        list_file_path = Path(self.output_dir) / "mylist.txt"

        # 使用项目名称构建最终视频文件名
        if self.project_name:
            final_video_filename = f"{self.project_name}.mp4"
        else:
            final_video_filename = "final_video.mp4"

        final_video_path = Path(self.output_dir) / final_video_filename

        try:
            with open(list_file_path, "w", encoding="utf-8") as f:
                for video_path in video_paths:
                    abs_video_path = Path(video_path).resolve()
                    safe_path_str = str(abs_video_path).replace("'", "'\\''")
                    f.write(f"file '{safe_path_str}'\n")
            logger.debug(f"Created ffmpeg file list: {list_file_path}")

            ffmpeg_cmd = [
                "ffmpeg",
                "-y",
                "-f",
                "concat",
                "-safe",
                "0",
                "-i",
                str(list_file_path.resolve()),
                "-c",
                "copy",
                str(final_video_path.resolve()),
            ]
            logger.debug(f"Running ffmpeg concatenation command: {' '.join(ffmpeg_cmd)}")

            subprocess.run(ffmpeg_cmd, check=True, capture_output=True, text=True, encoding="utf-8")
            logger.success(f"Successfully concatenated videos into: {final_video_path}")
            return str(final_video_path)

        except FileNotFoundError:
            logger.error(f"Error creating file list - Does directory exist? {list_file_path.parent}")
            return None
        except subprocess.CalledProcessError as concat_cpe:
            logger.error(f"Failed to concatenate videos. Error: {concat_cpe.stderr}")
            return None
        except Exception as concat_e:
            logger.error(f"An unexpected error occurred during video concatenation: {concat_e}")
            return None
        finally:
            if list_file_path.exists():
                try:
                    os.remove(list_file_path)
                    logger.debug(f"Removed temporary file list: {list_file_path}")
                except OSError as remove_err:
                    logger.warning(f"Could not remove temporary file list {list_file_path}: {remove_err}")

    def process_all(self, start_idx: int = 0, end_idx: Optional[int] = None) -> list[str]:
        """
        Process storyboard entries in parallel within the specified range, running specified stages.

        Args:
            start_idx: Index of the first entry to process (0-based)
            end_idx: Index of the last entry to process, exclusive (0-based)
            # stages_to_run_globally is now an instance variable self.stages_to_run_globally

        Returns:
            List of paths to the "final" video files for successfully processed entries.
            The definition of "final" depends on the stages run (e.g., subtitled or just rendered).
            If concatenation is successful and requested, this list might be less relevant if only final_video_path is needed.
        """
        storyboard = self.load_storyboard()
        if not storyboard:
            logger.error("No storyboard entries found")
            return []

        # Apply index limits
        if end_idx is None:
            end_idx = len(storyboard)

        start_idx = max(0, start_idx)
        end_idx = min(end_idx, len(storyboard))

        # Filter storyboard to only include entries in the specified range
        storyboard_slice = [(idx, storyboard[idx]) for idx in range(start_idx, end_idx)]

        # Use a dictionary to store results keyed by index to maintain order
        # Results will now be dictionaries of paths from _process_single_entry_stages
        results_map: dict[int, Optional[dict[str, Optional[str]]]] = {}
        processed_count = 0

        # Determine stages for individual entries (all except 'concat')
        stages_for_entry = [s for s in self.stages_to_run_globally if s != "concat"]

        # Use ThreadPoolExecutor for parallel processing
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit only the tasks in the specified range
            future_to_idx = {
                executor.submit(self.process_storyboard_entry_wrapper, idx, entry, stages_for_entry): idx
                for idx, entry in storyboard_slice
            }

            # Process results as they complete
            for future in concurrent.futures.as_completed(future_to_idx):
                idx = future_to_idx[future]
                try:
                    entry_result_paths = future.result()  # This is now a dict or None
                    if entry_result_paths:
                        results_map[idx] = entry_result_paths
                        # Check if a video was produced if render/subtitles were part of stages_for_entry
                        has_video_stages = any(s in stages_for_entry for s in ["render", "subtitles"])
                        final_video_for_entry = entry_result_paths.get(
                            "subtitled_video_file"
                        ) or entry_result_paths.get("rendered_video_file")

                        if has_video_stages and final_video_for_entry:
                            processed_count += 1  # Count as processed if video stages were run and video produced
                            logger.success(
                                f"Successfully processed entry {idx+1}, final video: {final_video_for_entry}"
                            )
                        elif not has_video_stages and all(
                            s in entry_result_paths for s in stages_for_entry if entry_result_paths.get(s)
                        ):  # Non-video stages succeeded
                            processed_count += (
                                1  # Count as processed if all requested non-video stages have output paths
                            )
                            logger.success(
                                f"Successfully completed non-video stages for entry {idx+1}. Outputs: {entry_result_paths}"
                            )
                        elif has_video_stages and not final_video_for_entry:
                            results_map[idx] = None  # Mark as failed if video stages requested but no video path
                            logger.error(
                                f"Entry {idx+1} ran video stages but no final video path was determined. Paths: {entry_result_paths}"
                            )
                        else:  # Other combination, possibly partial success of non-video stages
                            logger.warning(
                                f"Entry {idx+1} completed with paths: {entry_result_paths}, but not all requested outputs might be present or it's a partial non-video success."
                            )
                            # Decide if this counts as processed or not based on requirements. For now, we count it if entry_result_paths is not None.
                            # If entry_result_paths is None, it's already a failure.

                    else:  # future.result() was None, meaning all retries failed
                        results_map[idx] = None
                        logger.error(f"Failed to process entry {idx+1} after all retries.")
                except Exception as e:
                    results_map[idx] = None
                    logger.error(f"Error in future for storyboard entry {idx+1}: {e}")

        successful_videos_for_concat = []
        total_expected = end_idx - start_idx

        for i in range(start_idx, end_idx):
            entry_paths = results_map.get(i)
            if entry_paths:
                # Determine the "final" video for this entry for concatenation purposes
                video_to_concat = entry_paths.get("subtitled_video_file") or entry_paths.get("rendered_video_file")
                if video_to_concat:
                    successful_videos_for_concat.append(video_to_concat)
                elif any(s in self.stages_to_run_globally for s in ["render", "subtitles"]):  # If video was expected
                    logger.warning(
                        f"Entry {i+1} succeeded some stages but did not produce a video for concatenation. Paths: {entry_paths}"
                    )
            else:  # Entry i failed completely
                logger.warning(f"Entry {i+1} did not produce a successful result for any stage.")

        logger.info(
            f"Processed {processed_count} out of {total_expected} storyboard entries based on requested stages."
        )

        # --- Concatenate videos if requested and conditions met ---
        if "concat" in self.stages_to_run_globally:
            if successful_videos_for_concat:
                self._concatenate_videos(successful_videos_for_concat)
            elif not successful_videos_for_concat:
                logger.warning(
                    "Concatenation requested, but no videos were successfully processed/found for concatenation."
                )
            else:  # Some videos exist, but not all entries that should have video did.
                logger.warning(
                    "Concatenation requested, but not all storyboard entries that were expected to produce video succeeded. Skipping concatenation or concatenating available ones?"
                )
                # Current _concatenate_videos will proceed with what's in successful_videos_for_concat
                self._concatenate_videos(successful_videos_for_concat)

        # Return the list of "final" video paths from each successful entry (subtitled or rendered)
        # This return value might need to change to return the dicts from results_map for more detailed info.
        final_video_outputs = []
        for i in range(start_idx, end_idx):
            entry_paths = results_map.get(i)
            if entry_paths:
                final_vid = entry_paths.get("subtitled_video_file") or entry_paths.get("rendered_video_file")
                if final_vid:
                    final_video_outputs.append(final_vid)
        return final_video_outputs


def setup_logger():
    """Configure logger with appropriate format and level"""
    logger.remove()  # Remove default handler
    logger.add(
        sys.stderr,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO",
    )
    # Add file handler
    os.makedirs("logs", exist_ok=True)
    logger.add(
        "logs/storyboard_processing.log",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        level="DEBUG",
        rotation="500 MB",
    )


def main():
    """
    Process a storyboard JSON file and convert entries to videos
    """
    parser = argparse.ArgumentParser(description="Convert storyboard JSON to Manim animations")
    parser.add_argument(
        "storyboard_file", nargs="?", default="output/storyboard_mcp_nasdq.json", help="Path to storyboard JSON file"
    )
    parser.add_argument("--output-dir", "-o", default="output/videos", help="Directory to store output files")
    parser.add_argument("--max-workers", "-w", type=int, default=2, help="Maximum number of concurrent workers")
    parser.add_argument("--dsl-schema", "-s", default="docs/animation_functions.md", help="Path to DSL schema file")
    parser.add_argument(
        "--start-idx", "-i", type=int, default=0, help="Start processing from this storyboard index (0-based)"
    )
    parser.add_argument(
        "--end-idx", "-e", type=int, default=None, help="End processing at this storyboard index (0-based, exclusive)"
    )
    parser.add_argument(
        "--quality", "-q", type=str, default="l", choices=["l", "m", "h", "k"], help="Manim render quality (default: l)"
    )
    parser.add_argument(
        "--stages",
        type=str,
        default="dsl,code,render,subtitles,concat",
        help="Comma-separated list of stages to run: dsl, code, render, subtitles, concat. Default is all stages.",
    )
    parser.add_argument(
        "--project-name",
        type=str,
        default=None,
        help="Project name for the final video filename (default: final_video.mp4)",
    )

    args = parser.parse_args()

    # debug print args
    for arg in vars(args):
        print(f"{arg:<20} = {getattr(args, arg)}")

    # Set up logging
    setup_logger()

    # Log the script arguments
    logger.info("Starting storyboard processing with arguments:")
    logger.info(f"  Storyboard file: {args.storyboard_file}")
    logger.info(f"  Output directory: {args.output_dir}")
    logger.info(f"  Max workers: {args.max_workers}")
    logger.info(f"  DSL schema file: {args.dsl_schema}")
    logger.info(f"  Start index: {args.start_idx}")
    logger.info(f"  End index: {args.end_idx}")
    logger.info(f"  Quality: {args.quality}")
    logger.info(f"  Stages: {args.stages}")
    logger.info(f"  Project name: {args.project_name}")

    # Check if files exist
    if not os.path.isfile(args.storyboard_file):
        logger.error(f"Storyboard file not found: {args.storyboard_file}")
        return 1

    if not os.path.isfile(args.dsl_schema):
        logger.error(f"DSL schema file not found: {args.dsl_schema}")
        return 1

    # Create the processor
    processor = StoryboardProcessor(
        storyboard_file=args.storyboard_file,
        output_dir=args.output_dir,
        dsl_schema_file=args.dsl_schema,
        max_workers=args.max_workers,
        quality=args.quality,
        stages_str=args.stages,
        project_name=args.project_name,
    )

    # Load the storyboard to validate indices
    storyboard = processor.load_storyboard()
    if not storyboard:
        logger.error("No storyboard entries found")
        return 1

    # Validate indices
    if args.start_idx < 0 or args.start_idx >= len(storyboard):
        logger.error(f"Start index {args.start_idx} is out of range [0, {len(storyboard)-1}]")
        return 1

    end_idx = args.end_idx if args.end_idx is not None else len(storyboard)
    if end_idx <= args.start_idx or end_idx > len(storyboard):
        logger.error(f"End index {end_idx} is out of range [{args.start_idx+1}, {len(storyboard)}]")
        return 1

    # Process the storyboard entries in parallel
    # The stages are now handled by the StoryboardProcessor instance using self.stages_to_run_globally
    results = processor.process_all(start_idx=args.start_idx, end_idx=end_idx)

    # Summary
    # Results now contains paths to final videos of entries, if video stages were run.
    # The meaning of "success" depends on the stages run.
    if results:  # If any videos were produced (or other artifacts if only non-video stages run)
        # This success message might need to be more nuanced based on stages.
        # If only 'dsl' was run, `results` would be empty based on current return.
        # Consider changing `process_all` return value for better summary.
        # For now, this summary focuses on video outputs.
        if any(s in processor.stages_to_run_globally for s in ["render", "subtitles"]):
            logger.success(
                f"Successfully produced {len(results)} video outputs for the requested video-producing stages."
            )
            for i, result_path in enumerate(results):
                logger.info(f"  {i+1}. {result_path}")
        else:
            logger.success(f"Successfully completed requested non-video stages for {end_idx - args.start_idx} entries.")
            # Here, 'results' would be empty. Need to confirm if StoryboardProcessor.process_all should return artifact paths too.
            # For now, this is a generic success.
            # We could count successes within process_all for non-video stages and log that count.

    elif not results and any(s in processor.stages_to_run_globally for s in ["render", "subtitles"]):
        logger.error("Failed to produce any video outputs for the requested video-producing stages.")
    elif not processor.stages_to_run_globally:  # No stages requested
        logger.info("No stages were requested for processing.")
    else:  # Non-video stages requested but something might have gone wrong or no clear "result" list to show
        logger.warning("Processing completed. Check logs for details on non-video stages.")

    return 0


if __name__ == "__main__":
    sys.exit(main())
