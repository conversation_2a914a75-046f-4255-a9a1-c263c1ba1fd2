# GitHub项目体裁专用Prompt

## GitHub项目内容特色要求

### 内容理解重点
- **技术特性**: 突出项目的核心技术特性和亮点
- **实用价值**: 强调项目解决的实际问题和应用场景
- **吸引性**: 找到项目最具吸引力的地方（比如机构/作者、star数、突破性的功能）在开篇一句话介绍

### 分镜设计原则
- **项目概览**: 用star数、fork数等数据展示项目热度
- **问题解决**: 从项目解决的实际问题切入
- **特性演示**: 重点展示核心功能和使用效果


### 特殊处理要点
- **必须** 分镜之间的讲解逻辑要清晰，使用“然后”或者“接着”这种表达方式衔接
- **必须** 多用Readme里下载的图片、视频、GIF等素材
- **必须** markdown_content和overlay_text内容每一项前面增加一个合适的emoji表情,增加内容丰富度


