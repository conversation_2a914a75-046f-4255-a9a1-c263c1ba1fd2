"""
Theme Utilities
主题工具类，为动画函数提供便捷的主题样式获取和应用方法
"""

from typing import Any

from manim import *

from .theme_manager import get_theme_value


class ThemeUtils:
    """主题工具类，提供便捷的样式获取方法"""

    @staticmethod
    def get_color(color_name: str, fallback: str = WHITE) -> str:
        """
        获取主题颜色

        Args:
            color_name: 颜色名称，如 'primary', 'secondary', 'text_primary' 等
            fallback: 如果找不到颜色时的备选颜色

        Returns:
            颜色值
        """
        color = get_theme_value(f"colors.{color_name}", fallback)
        return color

    @staticmethod
    def get_cyclic_colors(count: int = None) -> list[str]:
        """
        获取循环使用颜色序列

        Args:
            count: 需要的颜色数量，如果为None则返回全部

        Returns:
            颜色列表
        """
        colors = get_theme_value("colors.cyclic_colors", [BLUE, RED, GREEN, YELLOW, PURPLE, ORANGE, TEAL])
        if count is None:
            return colors

        # 如果需要的颜色数量超过可用颜色，循环使用
        if count <= len(colors):
            return colors[:count]
        else:
            extended_colors = []
            for i in range(count):
                extended_colors.append(colors[i % len(colors)])
            return extended_colors

    @staticmethod
    def get_font(font_type: str = "primary", fallback: str = "Microsoft YaHei") -> str:
        """
        获取字体

        Args:
            font_type: 字体类型，可选 'primary', 'secondary', 'code'

        Returns:
            字体名称
        """
        return get_theme_value(f"typography.{font_type}_font", fallback)

    @staticmethod
    def get_font_size(size_type: str, fallback: int = 24) -> int:
        """
        获取字体大小

        Args:
            size_type: 字号类型，如 'display', 'h1', 'h2', 'h3', 'body', 'small', 'code'

        Returns:
            字号大小
        """
        return get_theme_value(f"typography.{size_type}_size", fallback)

    @staticmethod
    def get_spacing(spacing_type: str, fallback: float = 0.4) -> float:
        """
        获取间距

        Args:
            spacing_type: 间距类型，如 'xs', 'sm', 'md', 'lg', 'xl', 'xxl'
                         或特定组件间距如 'list_item_spacing', 'chart_element_spacing'

        Returns:
            间距值
        """
        return get_theme_value(f"spacing.{spacing_type}", fallback)

    @staticmethod
    def get_animation_duration(duration_type: str = "default", fallback: float = 2.0) -> float:
        """
        获取动画时长

        Args:
            duration_type: 时长类型，如 'default', 'fade', 'slide', 'highlight'

        Returns:
            动画时长
        """
        return get_theme_value(f"animation.{duration_type}_duration", fallback)

    @staticmethod
    def get_component_style(component: str, style: str, fallback: Any = None) -> Any:
        """
        获取组件样式

        Args:
            component: 组件类型，如 'background', 'list', 'table', 'chart', 'comparison', 'timeline'
            style: 样式属性

        Returns:
            样式值
        """
        if component == "background":
            return get_theme_value(f"components.background_{style}", fallback)
        elif component == "list":
            return get_theme_value(f"components.list_{style}", fallback)
        elif component == "table":
            return get_theme_value(f"components.table_{style}", fallback)
        elif component == "chart":
            return get_theme_value(f"components.chart_{style}", fallback)
        elif component == "comparison":
            return get_theme_value(f"components.comparison.{style}", fallback)
        elif component == "timeline":
            return get_theme_value(f"components.timeline.{style}", fallback)
        else:
            return get_theme_value(f"components.{style}", fallback)

    @staticmethod
    def create_themed_text(
        text: str, text_type: str = "body", color_type: str = "text_primary", font_type: str = "primary", **kwargs
    ) -> MarkupText:
        """
        创建应用主题样式的文本对象

        Args:
            text: 文本内容
            text_type: 文本类型，决定字号
            color_type: 颜色类型
            font_type: 字体类型
            **kwargs: 其介MarkupText参数

        Returns:
            MarkupText对象
        """
        font_size = ThemeUtils.get_font_size(text_type)
        color = ThemeUtils.get_color(color_type)
        font = ThemeUtils.get_font(font_type)

        # 合并参数
        text_kwargs = {"font_size": font_size, "color": color, "font": font, **kwargs}

        return MarkupText(text, **text_kwargs)

    @staticmethod
    def create_themed_background(
        mobject: Mobject,
        background_type: str = "surface",
        padding: float = None,
        corner_radius: float = None,
        opacity: float = None,
    ) -> VGroup:
        """
        为Mobject创建主题化的背景

        Args:
            mobject: 要添加背景的对象
            background_type: 背景颜色类型
            padding: 内边距，如果为None则使用主题设置
            corner_radius: 圆角半径，如果为None则使用主题设置
            opacity: 透明度，如果为None则使用主题设置

        Returns:
            包含背景和原对象的VGroup
        """
        if padding is None:
            padding = ThemeUtils.get_spacing("md")
        if corner_radius is None:
            corner_radius = ThemeUtils.get_component_style("background", "corner_radius")
        if opacity is None:
            opacity = ThemeUtils.get_component_style("background", "opacity")

        background_color = ThemeUtils.get_color(background_type)

        # 创建背景矩形
        background = RoundedRectangle(
            width=mobject.width + 2 * padding,
            height=mobject.height + 2 * padding,
            color=background_color,
            fill_opacity=opacity,
            stroke_width=0,
            corner_radius=corner_radius,
        )
        background.move_to(mobject.get_center())

        # 组合背景和内容
        group = VGroup()
        group.add(background, mobject)

        return group

    @staticmethod
    def create_themed_list_item(text: str, max_width: float = None, **kwargs) -> VGroup:
        """
        创建主题化的列表项

        Args:
            text: 列表项文本
            max_width: 最大宽度
            **kwargs: 其他参数

        Returns:
            包含背景的列表项VGroup
        """
        # 创建文本
        text_obj = ThemeUtils.create_themed_text(text, **kwargs)

        # 获取列表项样式
        bg_color = ThemeUtils.get_color("surface")
        padding = ThemeUtils.get_component_style("list", "padding")
        corner_radius = ThemeUtils.get_component_style("list", "corner_radius")
        opacity = ThemeUtils.get_component_style("list", "background_opacity")

        # 计算背景宽度
        if max_width is None:
            bg_width = text_obj.width + 2 * padding
        else:
            bg_width = max_width + 2 * padding

        # 创建背景
        background = RoundedRectangle(
            width=bg_width,
            height=text_obj.height + 2 * padding,
            color=bg_color,
            fill_opacity=opacity,
            stroke_width=0,
            corner_radius=corner_radius,
        )
        background.move_to(text_obj.get_center()).align_to(text_obj, LEFT)
        text_obj.shift(RIGHT * padding)

        # 组合
        item_group = VGroup()
        item_group.add(background, text_obj)

        return item_group


class ThemedAnimationMixin:
    """
    主题化动画混合类，为动画函数提供主题化的动画效果
    """

    @staticmethod
    def get_themed_animation(mobject: Mobject, animation_style: str = "fadeIn", **kwargs) -> Animation:
        """
        根据主题和动画样式创建动画

        Args:
            mobject: 要动画的对象
            animation_style: 动画样式
            **kwargs: 其他动画参数

        Returns:
            Animation对象
        """
        duration = ThemeUtils.get_animation_duration("default")

        # 合并动画参数
        anim_kwargs = {"run_time": duration, **kwargs}

        # 根据样式创建不同的动画
        if animation_style.lower() == "fadein":
            return FadeIn(mobject, **anim_kwargs)
        elif animation_style.lower() == "create":
            return Create(mobject, **anim_kwargs)
        elif animation_style.lower() == "write":
            return Write(mobject, **anim_kwargs)
        elif animation_style.lower() == "growarrow":
            return GrowArrow(mobject, **anim_kwargs)
        elif animation_style.lower() == "drawborderthenfill":
            return DrawBorderThenFill(mobject, **anim_kwargs)
        elif animation_style.lower() == "fadeshift":
            return FadeIn(mobject, shift=DOWN, **anim_kwargs)
        else:
            # 默认使用FadeIn
            return FadeIn(mobject, **anim_kwargs)

    @staticmethod
    def get_themed_lagged_animation(
        mobjects: list[Mobject], animation_style: str = "fadeIn", lag_ratio: float = None, **kwargs
    ) -> AnimationGroup:
        """
        创建主题化的延迟动画组

        Args:
            mobjects: 要动画的对象列表
            animation_style: 动画样式
            lag_ratio: 延迟比例，如果为None则使用主题设置
            **kwargs: 其他动画参数

        Returns:
            AnimationGroup对象
        """
        if lag_ratio is None:
            lag_ratio = get_theme_value("animation.lag_ratio", 2.0)

        animations = []
        for mobject in mobjects:
            anim = ThemedAnimationMixin.get_themed_animation(mobject, animation_style, **kwargs)
            animations.append(anim)

        return LaggedStart(*animations, lag_ratio=lag_ratio)


# 便捷的主题工具实例
theme_utils = ThemeUtils()
