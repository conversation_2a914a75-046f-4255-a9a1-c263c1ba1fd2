"""
This package contains all the individual animation function modules.
Importing these modules ensures that their @register_animation decorators are run,
populating the ANIMATION_FUNCTIONS registry in dsl.v2.core.animation_registry.
"""

from .animate_architecture_diagram import animate_architecture_diagram
from .animate_chart import animate_chart
from .animate_counter import animate_counter
from .animate_highlight_content import animate_highlight_content
from .animate_image import animate_image
from .animate_markdown import animate_markdown
from .animate_side_by_side_comparison import animate_side_by_side_comparison
from .animate_timeline import animate_timeline
from .animate_video import animate_video

__all__ = [
    "animate_architecture_diagram",
    "animate_chart",
    "animate_counter",
    "animate_timeline",
    "animate_image",
    "animate_markdown",
    "animate_video",
    "animate_side_by_side_comparison",
    "animate_highlight_content",
]
