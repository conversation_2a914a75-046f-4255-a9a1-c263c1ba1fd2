# animate_image

## 效果

在Manim场景中显示图像，支持缩放平移动画和注释文本。


## 使用场景

- 展示需要详细解释的图片或截图
- 展示图表、图解或可视化内容
- 显示产品或界面截图并添加注释说明

## 参数

| 参数名 | 类型 | 描述 | 是否必需 | 默认值 |
| ------ | ---- | ---- | -------- | ------ |
| scene | FeynmanScene | Manim场景实例（由系统自动传入） | 否 | - |
| image_path | str | 要显示的图片的本地文件路径 | 是 | - |
| id | str | 创建的Manim Mobject的唯一标识符 | 否 | None |
| narration | str | 在图片显示时播放的语音旁白文本 | 是 | - |
| annotation | str | 作为注释显示在图片旁边的文本，支持Markdown格式 | 否 | None |

## DSL示例

### 示例 1

```json
{
  "type": "animate_image",
  "params": {
    "image_path": "assets/manim_logo.png",
    "narration": "让我们看看这张图片。",
    "annotation": "这是一张示例图片，展示了重要的内容。"
  }
}
```

### 示例 2

```json
{
  "type": "animate_image",
  "params": {
    "image_path": "assets/manim_logo.png",
    "id": "architecture_diagram",
    "annotation": "## 系统架构\n- 前端组件\n- 后端服务\n- 数据存储\n",
    "narration": "这张架构图展示了系统的主要组件和它们之间的关系。"
  }
}
```

## 注意事项

- 图片文件必须存在且路径正确，否则会抛出FileNotFoundError
- 图片会自动缩放以适应场景，并根据图片比例可能会添加平移动画
- 如果提供annotation，它会作为文本显示在图片右侧

