# animate_chart

## 效果

创建并播放条形图、折线图或雷达图的动画，支持单个或多个数据集。


## 使用场景

- 可视化数据趋势和比较（折线图、条形图）
- 展示多个类别在不同指标上的表现（雷达图）
- 在视频演示中动态呈现统计数据
- 对比不同数据集之间的关系

## 参数

| 参数名 | 类型 | 描述 | 是否必需 | 默认值 |
| ------ | ---- | ---- | -------- | ------ |
| scene | FeynmanScene | Manim场景实例（由系统自动传入） | 否 | - |
| chart_type | str | 图表类型。可选值：'bar'（条形图）, 'line'（折线图）, 'radar'（雷达图） | 是 | - |
| data | dict | list[dict] | 图表数据。单数据集为dict格式（如{"A":10,"B":20}），多数据集为dict列表 | 是 | - |
| narration | str | 在图表显示时播放的语音旁白文本 | 是 | - |
| title | str | 图表标题 | 否 | None |
| animation_style | str | 图表入场动画。可选值：'fadeIn', 'grow', 'draw', 'update' | 否 | fadeIn |
| id | str | 创建的Manim Mobject的唯一标识符 | 否 | None |
| dataset_names | list[str] | 多数据集时，用于图例的数据集名称列表 | 否 | None |
| x_label | str | x轴标签（条形图和折线图有效） | 否 | None |
| y_label | str | y轴标签（条形图和折线图有效） | 否 | None |

## DSL示例

### 示例 1

```json
{
  "type": "animate_chart",
  "params": {
    "chart_type": "bar",
    "data": {
      "苹果": 75,
      "香蕉": 120,
      "橙子": 90
    },
    "title": "水果销量",
    "narration": "这是本月水果销量的条形图。",
    "x_label": "水果",
    "y_label": "销量 (千克)"
  }
}
```

### 示例 2

```json
{
  "type": "animate_chart",
  "params": {
    "chart_type": "line",
    "data": [
      {
        "第一季度": 50,
        "第二季度": 65,
        "第三季度": 80,
        "第四季度": 70
      },
      {
        "第一季度": 40,
        "第二季度": 50,
        "第三季度": 60,
        "第四季度": 90
      }
    ],
    "title": "产品A vs 产品B 季度销售额",
    "dataset_names": [
      "产品A",
      "产品B"
    ],
    "narration": "此折线图比较了产品A和产品B的季度销售额。",
    "x_label": "季度",
    "y_label": "销售额 (万元)"
  }
}
```

### 示例 3

```json
{
  "type": "animate_chart",
  "params": {
    "chart_type": "radar",
    "data": [
      {
        "性能": 8,
        "价格": 6,
        "外观": 9,
        "易用性": 7,
        "可靠性": 8
      },
      {
        "性能": 9,
        "价格": 4,
        "外观": 7,
        "易用性": 8,
        "可靠性": 9
      }
    ],
    "title": "产品对比",
    "dataset_names": [
      "产品X",
      "产品Y"
    ],
    "narration": "这个雷达图展示了两款产品在五个维度上的评分对比。"
  }
}
```

## 注意事项

- 对于条形图和折线图，数据键作为x轴标签，值作为y轴数据点
- 对于雷达图，数据键作为各个轴的标签，值作为该轴上的数据点
- 如果提供多个数据集，建议同时提供dataset_names以便在图例中显示
- update动画风格对条形图有特殊效果，会显示数值从0到目标值的变化过程

