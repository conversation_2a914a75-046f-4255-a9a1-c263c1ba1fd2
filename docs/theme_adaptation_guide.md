# 主题适配指南

本指南总结了将现有组件适配到主题系统的步骤和注意事项，可用于指导后续组件的主题化过程。

## 1. 主题系统概述

Feynman项目使用了一个集中式主题管理系统，通过 `ThemeManager` 和 `ThemeUtils` 类提供，主题配置存储在 `Theme` 数据类中。主要文件：

- `theme_manager.py`: 定义主题结构和管理主题切换
- `theme_utils.py`: 提供便捷访问主题属性的工具方法

主题属性包括：
- 颜色（主色、次色、背景色、文本色等）
- 字体和字号
- 间距和边距
- 动画参数（持续时间、缓动函数）
- 组件特定样式

## 2. 主题适配基本步骤

### 2.1 准备阶段

1. **添加必要的导入**
   ```python
   from dsl.v2.themes.theme_utils import ThemeUtils
   ```

2. **分析现有代码**
   - 识别所有硬编码的颜色、字体、尺寸和间距
   - 识别动画参数（如持续时间）
   - 了解代码结构和组件创建过程

### 2.2 替换硬编码样式

3. **替换颜色**
   - 使用 `ThemeUtils.get_color(color_name, fallback)`，例如：
     ```python
     # 替换前
     color=WHITE
     # 替换后
     color=ThemeUtils.get_color("text_primary")
     ```

4. **替换字体和字号**
   - 使用 `ThemeUtils.get_font()` 和 `ThemeUtils.get_font_size()`：
     ```python
     # 替换前
     font="Maple Mono NF CN", font_size=24
     # 替换后
     font=ThemeUtils.get_font("primary"), font_size=ThemeUtils.get_font_size("body")
     ```

5. **替换间距**
   - 使用 `ThemeUtils.get_spacing()`：
     ```python
     # 替换前
     buff=0.3
     # 替换后
     buff=ThemeUtils.get_spacing("md")
     ```

6. **替换组件特定样式**
   - 使用 `ThemeUtils.get_component_style()`：
     ```python
     # 替换前
     stroke_width=2
     # 替换后
     stroke_width=ThemeUtils.get_component_style("chart", "chart_line_width", 2.0)
     ```

7. **替换动画参数**
   - 使用 `ThemeUtils.get_animation_duration()`：
     ```python
     # 替换前
     run_time=1.5
     # 替换后
     run_time=ThemeUtils.get_animation_duration("fade")
     ```

## 3. 不同元素的适配方法

### 3.1 文本元素

对于简单文本，使用 `ThemeUtils.create_themed_text()`：
```python
# 替换前
title_obj = Text(title, font_size=32, color=BLUE, font="Maple Mono NF CN")
# 替换后
title_obj = ThemeUtils.create_themed_text(title, "h2", "primary")
```

### 3.2 形状和背景

替换填充颜色和描边颜色：
```python
# 替换前
background = Rectangle(fill_color=BLACK, fill_opacity=0.6)
# 替换后
background = Rectangle(
    fill_color=ThemeUtils.get_color("background"),
    fill_opacity=ThemeUtils.get_component_style("background", "background_opacity", 0.6)
)
```

### 3.3 图表元素

对于图表，需要设置多个配置项：
```python
# 轴线和刻度配置
axis_config={
    "color": ThemeUtils.get_color("text_secondary"),
    "stroke_width": ThemeUtils.get_component_style("chart", "axis_stroke_width", 1.0)
}

# Y轴标签配置
y_axis_config={
    "font_size": ThemeUtils.get_font_size("body"), 
    "label_color": ThemeUtils.get_color("text_primary"), 
    "color": ThemeUtils.get_color("text_secondary")
}
```

### 3.4 背景和渐变

对于渐变背景，使用主题颜色：
```python
gradient = Rectangle(
    width=config.frame_width, 
    height=config.frame_height, 
    fill_opacity=1, 
    stroke_width=0
).set_color_by_gradient([primary_color, background_color, primary_color])
```

## 4. 测试和验证

1. **切换主题测试**
   - 在 `config.yaml` 中修改主题（light/dark）
   - 确认所有元素正确响应主题变化

2. **检查关键点**
   - 文本可读性（确保背景和文本对比度）
   - 图表可辨识性（确保不同数据集颜色区分度）
   - 动画一致性（确保动画持续时间与主题设置一致）

## 5. 注意事项

1. **增量修改**
   采用增量方式进行修改，每次完成一个小部分并测试，而非一次性大规模修改。

2. **默认值**
   始终为主题属性提供合理的默认值，以防主题系统中没有定义相应属性：
   ```python
   ThemeUtils.get_color("primary", fallback=BLUE)
   ```

3. **Manim特殊处理**
   某些Manim对象可能需要在创建后单独设置颜色：
   ```python
   # 有些情况下需要这样设置
   base_chart.y_axis.set_color(axis_color)
   ```

4. **字体兼容性**
   确保使用的字体在所有预期环境中可用，或提供适当的回退选项。

5. **组件特殊性**
   某些组件可能有特殊的颜色要求，需要根据组件特性选择合适的主题颜色。

6. **性能考虑**
   复杂的动态样式计算可能影响性能，对于频繁使用的计算结果考虑缓存。

## 6. 完整工作流程

1. 分析文件并识别需要主题化的元素
2. 添加必要的导入
3. 替换硬编码的颜色和样式
4. 替换字体、字号和间距
5. 替换动画参数
6. 测试不同主题下的表现
7. 优化和修复任何问题

按照本指南进行主题适配，可以确保整个应用保持一致的视觉风格，并支持主题切换功能。
