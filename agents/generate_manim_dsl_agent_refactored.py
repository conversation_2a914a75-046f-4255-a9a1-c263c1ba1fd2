#!/usr/bin/env python3
"""
Manim <PERSON>L生成代理 - 重构简化版本
基于内容体裁类型的模块化prompt体系
支持论文、GitHub项目、博客、案例研究、书籍等不同体裁
"""

import json
import logging
import os
import re
import sys
from typing import Dict, List, Optional, Any
from enum import Enum

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from camel.agents import ChatAgent
from camel.messages import BaseMessage
from utils.common import (
    MediaClassifier,
    Config,
    AgentFactory,
    CommonUtils
)

# 配置日志 - 确保体裁检测日志正常显示
logger = CommonUtils.setup_logging()
# 确保当前模块的日志级别为INFO
logger.setLevel(logging.INFO)

class ContentGenre(Enum):
    """内容体裁类型"""
    PAPER = "paper"           # 学术论文（通用）
    REVIEW_PAPER = "review_paper"  # 综述论文
    TECH_REPORT = "tech_report"    # 技术报告论文
    GITHUB = "github"         # GitHub项目
    BLOG = "blog"             # 网页博客
    CASE_STUDY = "case_study" # 综合案例
    BOOK = "book"             # 本地书籍
    GENERAL = "general"       # 通用内容

class GenreDetector:
    """内容体裁检测器"""

    @staticmethod
    def detect_genre(content: str, purpose: str = "") -> ContentGenre:
        """
        根据用户目的检测体裁类型
        """
        purpose_lower = purpose.lower()
        logger.info(f"开始体裁检测 - 用户目的: '{purpose}'")

        # 从用户目的中提取类型 - 优先检测特定论文类型
        if any(keyword in purpose_lower for keyword in ['综述', 'review', '文献综述', '研究综述', '综述论文']):
            logger.info("✅ 检测到关键词匹配 -> 综述论文体裁")
            return ContentGenre.REVIEW_PAPER
        elif any(keyword in purpose_lower for keyword in ['技术报告', 'technical report', '技术文档', '报告论文', '技术总结']):
            logger.info("✅ 检测到关键词匹配 -> 技术报告论文体裁")
            return ContentGenre.TECH_REPORT
        elif any(keyword in purpose_lower for keyword in ['论文', 'paper', '学术', '研究']):
            logger.info("✅ 检测到关键词匹配 -> 学术论文体裁")
            return ContentGenre.PAPER
        elif any(keyword in purpose_lower for keyword in ['github', '项目', '开源', 'repository']):
            logger.info("✅ 检测到关键词匹配 -> GitHub项目体裁")
            return ContentGenre.GITHUB
        elif any(keyword in purpose_lower for keyword in ['博客', 'blog', '文章', '经验']):
            logger.info("✅ 检测到关键词匹配 -> 博客文章体裁")
            return ContentGenre.BLOG
        elif any(keyword in purpose_lower for keyword in ['案例', 'case', '实战', '项目实施']):
            logger.info("✅ 检测到关键词匹配 -> 案例研究体裁")
            return ContentGenre.CASE_STUDY
        elif any(keyword in purpose_lower for keyword in ['书籍', 'book', '著作', '读书']):
            logger.info("✅ 检测到关键词匹配 -> 书籍体裁")
            return ContentGenre.BOOK

        # 默认为通用类型
        logger.info("⚠️ 未检测到特定关键词，使用通用体裁")
        return ContentGenre.GENERAL

class PromptLoader:
    """Prompt文件加载器"""

    def __init__(self, prompts_dir: str = "prompts/storyboard_prompts"):
        self.prompts_dir = prompts_dir
        self.base_template = None
        self.genre_templates = {}
        self._load_templates()

    def _load_templates(self):
        """加载所有prompt模板"""
        # 加载基础模板
        base_path = os.path.join(self.prompts_dir, "base_prompt.md")
        if os.path.exists(base_path):
            self.base_template = CommonUtils.read_file(base_path)
            logger.info("已加载基础prompt模板")
        else:
            logger.warning(f"基础prompt模板不存在: {base_path}")
            self.base_template = self._get_fallback_template()

        # 加载各体裁专用模板
        genre_files = {
            ContentGenre.PAPER: "paper_prompt.md",
            ContentGenre.REVIEW_PAPER: "review_paper_prompt.md",
            ContentGenre.TECH_REPORT: "tech_report_prompt.md",
            ContentGenre.GITHUB: "github_prompt.md",
            ContentGenre.BLOG: "blog_prompt.md",
            ContentGenre.CASE_STUDY: "case_study_prompt.md",
            ContentGenre.BOOK: "book_prompt.md"
        }

        for genre, filename in genre_files.items():
            file_path = os.path.join(self.prompts_dir, filename)
            if os.path.exists(file_path):
                self.genre_templates[genre] = CommonUtils.read_file(file_path)
                logger.info(f"已加载{genre.value}体裁prompt模板")
            else:
                logger.warning(f"体裁prompt模板不存在: {file_path}")

    def get_base_template(self) -> str:
        """获取基础模板"""
        return self.base_template

    def get_genre_requirements(self, genre: ContentGenre) -> str:
        """获取特定体裁的要求"""
        if genre in self.genre_templates:
            return f"\n**体裁专用要求**:\n{self.genre_templates[genre]}"
        return ""

    def _get_fallback_template(self) -> str:
        """获取默认模板"""
        return """
根据以下Markdown内容生成Manim DSL分镜脚本。

**Markdown内容**: {markdown_content}
**重要多媒体素材**: {important_media_info}
**用户目的**: {purpose}
**内容体裁**: {content_genre}

{genre_specific_requirements}

请生成JSON格式的分镜脚本。
"""

class PromptBuilder:
    """模块化的Prompt构建器 - 基于内容体裁"""

    def __init__(self, prompts_dir: str = "prompts/storyboard_prompts"):
        # 加载prompt模板
        self.prompt_loader = PromptLoader(prompts_dir)

        # 体裁检测器
        self.genre_detector = GenreDetector()

    def build_prompt(self, markdown_content: str, multimedia_elements: Dict, purpose: str) -> str:
        """构建完整的prompt"""
        # 1. 检测内容体裁
        genre = self.genre_detector.detect_genre(markdown_content, purpose)
        logger.info(f"检测到内容体裁: {genre.value}")

        # 2. 构建重要素材信息
        important_media_info = self._build_media_info(multimedia_elements)

        # 3. 获取体裁特定要求
        genre_specific_requirements = self.prompt_loader.get_genre_requirements(genre)

        # 4. 填充基础模板
        base_template = self.prompt_loader.get_base_template()

        return base_template.format(
            markdown_content=markdown_content[:10000],
            important_media_info=important_media_info,
            purpose=purpose if purpose else "制作清晰易懂的讲解视频",
            content_genre=genre.value,
            genre_specific_requirements=genre_specific_requirements
        )

    def _build_media_info(self, multimedia_elements: Dict) -> str:
        """构建重要素材信息"""
        if not multimedia_elements['important_media']:
            return "无重要多媒体素材"

        media_info = "重要多媒体素材（优先使用）：\n"
        for i, media in enumerate(multimedia_elements['important_media'][:8]):  # 最多8个
            media_type = media.get('type', 'unknown')
            description = media.get('description', '无描述')
            url = media.get('url', media.get('alt', ''))
            media_info += f"{i+1}. [{media_type}] {description} - {url}\n"

        return media_info

class MultimediaExtractor:
    """简化的多媒体提取器，专门用于DSL生成"""

    @staticmethod
    def extract_elements(content: str) -> Dict[str, List[Dict]]:
        """提取并分类多媒体元素，重点关注重要素材"""
        elements = {
            'videos': [],
            'images': [],
            'gifs': [],
            'important_media': []  # 重要素材优先使用
        }

        # 提取视频链接（包括MP4, GIF等）
        video_patterns = [
            r'\[([^\]]*)\]\(([^)]*\.(?:mp4|avi|mov|wmv|flv|webm|gif)[^)]*)\)',  # 标准链接格式
            r'!\[([^\]]*)\]\(([^)]*\.(?:mp4|avi|mov|wmv|flv|webm|gif)[^)]*)\)'  # 图片格式但是视频文件
        ]

        for pattern in video_patterns:
            for match in re.finditer(pattern, content):
                text, url = match.groups()
                element_text = f"{text} {url}"
                is_important, category, enhanced_desc = MediaClassifier.classify_importance(element_text, "video")

                video_info = {
                    'text': text,
                    'url': url,
                    'is_important': is_important,
                    'category': category,
                    'description': enhanced_desc,
                    'type': 'gif' if url.lower().endswith('.gif') else 'video'
                }

                if url.lower().endswith('.gif'):
                    elements['gifs'].append(video_info)
                else:
                    elements['videos'].append(video_info)

                if is_important:
                    elements['important_media'].append(video_info)

        # 提取图片
        image_pattern = r'!\[([^\]]*)\]\(([^)]+\.(?:png|jpg|jpeg|svg|webp)[^)]*)\)'
        for match in re.finditer(image_pattern, content):
            alt_text, url = match.groups()
            element_text = f"{alt_text} {url}"
            is_important, category, enhanced_desc = MediaClassifier.classify_importance(element_text, "image")

            image_info = {
                'alt': alt_text,
                'url': url,
                'is_important': is_important,
                'category': category,
                'description': enhanced_desc,
                'type': 'image'
            }
            elements['images'].append(image_info)
            if is_important:
                elements['important_media'].append(image_info)

        logger.info(f"提取多媒体元素: 视频{len(elements['videos'])}个, 图片{len(elements['images'])}个, "
                   f"GIF{len(elements['gifs'])}个, 重要素材{len(elements['important_media'])}个")

        return elements

class ManimDSLGeneratorAgent:
    """基于内容体裁的Manim DSL生成代理"""

    def __init__(self, config_path: str = "config/config.yaml", prompts_dir: str = "prompts/storyboard_prompts"):
        self.config = Config(config_path)
        self.model = AgentFactory.create_model(self.config)
        self.agent = self._create_agent()
        self.extractor = MultimediaExtractor()
        self.prompt_builder = PromptBuilder(prompts_dir)

    def _create_agent(self) -> ChatAgent:
        """创建DSL生成代理"""
        system_message = BaseMessage.make_assistant_message(
            role_name="Manim DSL Generator",
            content="你是将Markdown文档转换为Manim DSL脚本的专家。你能够根据不同内容体裁和素材类型自适应地设计最合适的分镜方案。"
        )
        return ChatAgent(system_message=system_message, model=self.model)

    def generate_dsl(self, markdown_content: str, purpose: str = "") -> str:
        """根据Markdown内容生成Manim DSL"""
        logger.info("开始生成Manim DSL")

        # 1. 提取多媒体元素
        multimedia_elements = self.extractor.extract_elements(markdown_content)

        # 2. 检测并提取timeline数据
        timeline_data = self._extract_timeline_data(markdown_content)

        # 3. 使用PromptBuilder构建prompt
        generation_prompt = self.prompt_builder.build_prompt(
            markdown_content, multimedia_elements, purpose
        )

        # 4. 生成DSL
        dsl_content = self._call_model_and_parse(generation_prompt, "生成DSL")

        # 5. 如果有timeline数据，添加timeline action
        if timeline_data:
            dsl_content = self._inject_timeline_action(dsl_content, timeline_data)

        return dsl_content

    def _extract_timeline_data(self, markdown_content: str) -> Optional[Dict]:
        """从markdown内容中提取timeline数据"""
        try:
            # 方法1: 检查是否有timeline章节
            if "## 📅 时间轴" in markdown_content:
                logger.info("检测到markdown中包含时间轴章节")

            # 方法2: 查找timeline_data.json文件
            if hasattr(self, 'markdown_file_path') and self.markdown_file_path:
                # 从markdown文件路径推断timeline数据文件位置
                markdown_dir = os.path.dirname(self.markdown_file_path)
                timeline_file = os.path.join(markdown_dir, 'timeline_data.json')

                if os.path.exists(timeline_file):
                    logger.info(f"找到timeline数据文件: {timeline_file}")
                    with open(timeline_file, 'r', encoding='utf-8') as f:
                        timeline_data = json.load(f)

                    # 验证timeline数据格式
                    if (timeline_data.get('suitable', False) and
                        timeline_data.get('events') and
                        len(timeline_data['events']) > 0):
                        logger.info(f"✅ 成功加载timeline数据，包含 {len(timeline_data['events'])} 个事件")
                        return timeline_data
                    else:
                        logger.info("timeline数据不适合生成时间轴")
                else:
                    logger.debug(f"未找到timeline数据文件: {timeline_file}")

            return None

        except Exception as e:
            logger.error(f"提取timeline数据失败: {e}")
            return None

    def _inject_timeline_action(self, dsl_content: str, timeline_data: Dict) -> str:
        """将timeline action注入到DSL中"""
        try:
            # 解析现有的DSL
            dsl_list = json.loads(dsl_content)

            # 创建timeline action
            timeline_action = {
                "分镜名": "时间轴展示",
                "内容要点": [f"{event.get('year', '')}: {event.get('title', '')}"
                           for event in timeline_data.get('events', [])[:3]],  # 只显示前3个
                "分镜内容": timeline_data.get('intro_narration', '以下是相关事件的时间轴展示'),
                "素材名": "时间轴数据",
                "视觉动效建议": [
                    {
                        "type": "animate_timeline",
                        "events": timeline_data.get('events', []),
                        "title": "发展历程",
                        "intro_narration": timeline_data.get('intro_narration'),
                        "outro_narration": timeline_data.get('outro_narration'),
                        "content_narration": timeline_data.get('content_narration'),
                        "target_region_id": "full_screen"
                    }
                ]
            }

            # 将timeline action插入到合适的位置（通常在第2个位置，作为概览）
            if len(dsl_list) >= 2:
                dsl_list.insert(1, timeline_action)
            else:
                dsl_list.append(timeline_action)

            logger.info("✅ 成功注入timeline action到DSL中")
            return json.dumps(dsl_list, ensure_ascii=False, indent=2)

        except Exception as e:
            logger.error(f"注入timeline action失败: {e}")
            return dsl_content  # 返回原始内容

    def _call_model_and_parse(self, prompt: str, operation_name: str) -> str:
        """调用大模型并解析JSON - 公共方法"""
        try:
            user_message = BaseMessage.make_user_message(role_name="User", content=prompt)
            self.agent.reset()
            response = self.agent.step(user_message)

            if not response.msgs:
                logger.error(f"{operation_name}阶段AI代理未返回响应")
                return self._get_fallback_dsl()

            generated_content = response.msgs[0].content
            # logger.info(f"收到{operation_name}响应，长度: {len(generated_content)}字符")  # 关闭详细响应日志

            # 解析和验证JSON
            return self._parse_and_validate_json(generated_content)

        except Exception as e:
            logger.error(f"{operation_name}过程出错: {e}")
            return self._get_fallback_dsl()

    def _parse_and_validate_json(self, content: str) -> str:
        """解析和验证JSON内容 - 简化版本"""
        content = content.strip()

        # 方法1：直接解析
        try:
            parsed_json = json.loads(content)
            if isinstance(parsed_json, list) and len(parsed_json) > 0:
                logger.info(f"✅ 直接JSON解析成功，分镜数量: {len(parsed_json)}")
                return json.dumps(parsed_json, ensure_ascii=False, indent=2)
        except:
            pass

        # 方法2：提取代码块（简化版）
        if '```' in content:
            # 找到第一个[和最后一个]之间的内容
            start = content.find('[')
            end = content.rfind(']') + 1
            if start != -1 and end > start:
                json_content = content[start:end]
                try:
                    parsed_json = json.loads(json_content)
                    if isinstance(parsed_json, list) and len(parsed_json) > 0:
                        logger.info(f"✅ 代码块JSON解析成功，分镜数量: {len(parsed_json)}")
                        return json.dumps(parsed_json, ensure_ascii=False, indent=2)
                except:
                    pass

        # 方法3：返回默认
        logger.error("JSON解析失败，使用默认DSL")
        return self._get_fallback_dsl()

    def _get_fallback_dsl(self) -> str:
        """返回默认的DSL结构"""
        fallback = [
            {
                "视觉动效建议": [
                    {
                        "type": "animate_markdown",
                        "content": "# 内容概览\n\n本视频将为您介绍相关技术内容",
                        "narration": "欢迎观看本期视频，我们将深入探讨相关技术内容。"
                    }
                ]
            },
            {
                "视觉动效建议": [
                    {
                        "type": "animate_markdown",
                        "content": "## 核心要点\n\n- 重要概念介绍\n- 关键技术特性\n- 实际应用价值",
                        "narration": "让我们来看看这项技术的核心要点和关键特性。"
                    }
                ]
            },
            {
                "视觉动效建议": [
                    {
                        "type": "animate_markdown",
                        "content": "## 总结\n\n通过本期内容，我们了解了相关技术的核心价值。",
                        "narration": "总结来说，这项技术具有重要的应用价值和发展前景。"
                    }
                ]
            }
        ]

        logger.info("使用默认DSL结构")
        return json.dumps(fallback, ensure_ascii=False, indent=2)

    def save_dsl(self, dsl_content: str, output_file: str = None) -> str:
        """保存生成的DSL内容"""
        if not output_file:
            output_file = f"output/manim_dsl_{int(__import__('time').time())}.json"

        # 确保有有效的文件路径
        if not output_file.strip():
            output_file = f"manim_dsl_{int(__import__('time').time())}.json"

        success = CommonUtils.save_file(dsl_content, output_file)
        if not success:
            # 如果保存失败，尝试保存到当前目录
            fallback_file = f"dsl_output_{int(__import__('time').time())}.json"
            logger.warning(f"原路径保存失败，尝试保存到: {fallback_file}")
            CommonUtils.save_file(dsl_content, fallback_file)
            return fallback_file

        return output_file

    def run(self, markdown_content: str, purpose: str = "", output_file: str = None, markdown_file_path: str = None) -> Dict[str, Any]:
        """主运行方法"""
        try:
            logger.info("开始生成Manim DSL")
            logger.info(f"输入内容长度: {len(markdown_content)}字符")
            logger.info(f"用户目的: {purpose}")

            # 设置markdown文件路径，用于查找timeline数据
            self.markdown_file_path = markdown_file_path

            # 1. 生成DSL
            dsl_content = self.generate_dsl(markdown_content, purpose)

            # 2. 保存结果
            saved_file = self.save_dsl(dsl_content, output_file)

            # 3. 返回结果
            return {
                'status': 'success',
                'output_file': saved_file,
                'purpose': purpose,
                'dsl_length': len(dsl_content),
                'scene_count': len(json.loads(dsl_content)) if dsl_content.startswith('[') else 0,
                'message': 'DSL生成完成'
            }

        except Exception as e:
            logger.error(f"DSL生成失败: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'message': 'DSL生成失败'
            }

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="基于体裁的Manim DSL生成工具")
    parser.add_argument("--markdown", "-m", type=str, required=True, help="输入Markdown文件路径")
    parser.add_argument("--purpose", "-p", type=str, help="用户目的描述（用于体裁检测）")
    parser.add_argument("--output", "-o", type=str, help="输出DSL文件路径")
    parser.add_argument("--config", "-c", type=str, default="config/config.yaml", help="配置文件路径")

    args = parser.parse_args()

    try:
        # 创建agent
        agent = ManimDSLGeneratorAgent(args.config)

        # 检查输入文件
        if not os.path.exists(args.markdown):
            print(f"❌ 错误: 输入文件不存在: {args.markdown}")
            return 1

        # 读取markdown内容
        markdown_content = CommonUtils.read_file(args.markdown)
        if not markdown_content:
            print(f"❌ 错误: 文件内容为空: {args.markdown}")
            return 1

        print(f"📖 输入文件: {args.markdown}")
        print(f"📝 内容长度: {len(markdown_content)} 字符")
        print(f"🎯 用户目的: {args.purpose if args.purpose else '默认视频制作'}")
        if args.output:
            print(f"💾 输出文件: {args.output}")

        # 执行处理
        result = agent.run(markdown_content, args.purpose or "", args.output, args.markdown)

        if result['status'] == 'success':
            print(f"\n✅ DSL生成完成！")
            print(f"输出文件: {result['output_file']}")
            print(f"分镜数量: {result['scene_count']} 个")
            print(f"DSL长度: {result['dsl_length']} 字符")
        else:
            print(f"\n❌ DSL生成失败: {result['error']}")
            return 1
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        return 1

    return 0

if __name__ == "__main__":
    exit(main())