#!/usr/bin/env python3
"""
材料代理 - 重构简化版本
移除过度设计的逻辑，专注于核心功能
参考github_source_agent_refactored.py的重构思路
"""

import logging
import os
import re
import sys
from pathlib import Path
from typing import Dict, List, Optional, Any

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.common import (
    MediaClassifier, 
    Config, 
    AgentFactory, 
    CommonUtils
)
from agents.material_enhancement import MaterialEnhancer

# 配置日志
logger = CommonUtils.setup_logging()
# 确保当前模块的日志级别为INFO
logger.setLevel(logging.INFO)

class MultimediaExtractor:
    """简化的多媒体提取器"""
    
    @staticmethod
    def extract_elements(content: str) -> Dict[str, List[Dict]]:
        """提取并分类多媒体元素"""
        elements = {
            'images': [],
            'videos': [],
            'code_blocks': [],
            'tables': [],
            'important_media': []  # 重要素材单独列出
        }
        
        # 提取图片
        image_pattern = r'!\[([^\]]*)\]\(([^)]+)\)'
        for match in re.finditer(image_pattern, content):
            alt_text, url = match.groups()
            element_text = f"{alt_text} {url}"
            is_important, category, enhanced_desc = MediaClassifier.classify_importance(element_text, "image")
            
            image_info = {
                'alt': alt_text,
                'url': url,
                'is_important': is_important,
                'category': category,
                'description': enhanced_desc
            }
            elements['images'].append(image_info)
            if is_important:
                elements['important_media'].append(image_info)
        
        # 提取视频链接
        video_pattern = r'\[([^\]]*)\]\(([^)]*\.(?:mp4|avi|mov|wmv|flv|webm)[^)]*)\)'
        for match in re.finditer(video_pattern, content):
            text, url = match.groups()
            element_text = f"{text} {url}"
            is_important, category, enhanced_desc = MediaClassifier.classify_importance(element_text, "video")
            
            video_info = {
                'text': text,
                'url': url,
                'is_important': is_important,
                'category': category,
                'description': enhanced_desc
            }
            elements['videos'].append(video_info)
            if is_important:
                elements['important_media'].append(video_info)
        
        # 提取代码块
        code_pattern = r'```(\w*)\n(.*?)\n```'
        for match in re.finditer(code_pattern, content, re.DOTALL):
            language, code = match.groups()
            element_text = f"{language} {code[:100]}"  # 只取前100字符分析
            is_important, category, enhanced_desc = MediaClassifier.classify_importance(element_text, "code")
            
            code_info = {
                'language': language,
                'code': code,
                'is_important': is_important,
                'category': category,
                'description': enhanced_desc
            }
            elements['code_blocks'].append(code_info)
            if is_important:
                elements['important_media'].append(code_info)
        
        # 提取表格（简化检测）
        table_pattern = r'\|.*\|.*\n\|[-\s|]+\|.*\n(\|.*\|.*\n)+'
        for match in re.finditer(table_pattern, content):
            table_content = match.group(0)
            element_text = table_content[:200]  # 只取前200字符分析
            is_important, category, enhanced_desc = MediaClassifier.classify_importance(element_text, "table")
            
            table_info = {
                'content': table_content,
                'is_important': is_important,
                'category': category,
                'description': enhanced_desc
            }
            elements['tables'].append(table_info)
            if is_important:
                elements['important_media'].append(table_info)
        
        logger.info(f"提取多媒体元素: 图片{len(elements['images'])}个, 视频{len(elements['videos'])}个, "
                   f"代码块{len(elements['code_blocks'])}个, 表格{len(elements['tables'])}个, "
                   f"重要素材{len(elements['important_media'])}个")
        
        return elements

class MaterialAgent:
    """简化的材料代理主类"""
    
    def __init__(self, config_path: str = "config/config.yaml", enhancement_config: Optional[Dict[str, bool]] = None):
        """
        初始化材料代理
        
        Args:
            config_path: 配置文件路径
            enhancement_config: 扩充策略配置
        """
        self.config = Config(config_path)
        self.model = AgentFactory.create_model(self.config)
        self.agents = self._create_agents()
        self.extractor = MultimediaExtractor()
        # 传递完整的config字典给MaterialEnhancer，使用self.config.config而不是self.config.data
        self.enhancer = MaterialEnhancer(enhancement_config, self.config.config)
    
    def _create_agents(self) -> Dict:
        """创建AI代理"""
        return {
            "generator": AgentFactory.create_analyzer_agent(
                self.model,
                "内容生成专家",
                "你负责根据用户目的和原始材料生成优化的视频讲解内容。专注于突出核心认知、关键结论和重要发现。"
            ),
            "reviewer": AgentFactory.create_reviewer_agent(
                self.model,
                "内容审查员", 
                "你负责审查和完善内容，确保逻辑清晰、重点突出且易于理解。"
            )
        }
    
    def _calculate_target_length(self, purpose: str) -> int:
        """根据purpose中的视频时长计算目标markdown长度"""
        # 提取时长数字（支持"5分钟"、"10分钟"、"25分钟"等格式）
        import re
        time_match = re.search(r'(\d+)\s*分钟', purpose)
        if time_match:
            minutes = int(time_match.group(1))
        else:
            # 如果没有明确时长，默认10分钟
            minutes = 10
        
        # 简单映射：每分钟大约800-1000字符的markdown内容
        base_chars_per_minute = 900
        target_length = minutes * base_chars_per_minute
        
        # 设置合理的上下限
        min_length = 2000   # 最少2000字符
        max_length = 30000  # 最多30000字符
        
        return max(min_length, min(target_length, max_length))
    
    def read_material(self, material_path: str) -> str:
        """读取材料内容"""
        if not material_path:
            return ""
        return CommonUtils.read_file(material_path)

    def generate_material(self, material_content: str, purpose: str, output_dir: str = "output") -> str:
        """生成优化的材料内容 - 使用新的扩充架构"""
        # logger.info("开始生成优化材料")  # 关闭详细生成日志
        
        # 0. 计算目标内容长度
        target_length = self._calculate_target_length(purpose)
        logger.info(f"根据视频时长计算目标内容长度: {target_length} 字符")
        
        # 1. 素材扩充步骤（统一管理各种扩充）
        enhancement_result = self.enhancer.enhance_material(material_content, output_dir)
        intro_content = enhancement_result.get('intro_content', '')
        
        # 2. 提取多媒体元素并修正路径
        multimedia_elements = {}
        important_media_info = ""
        if material_content:
            multimedia_elements = self.extractor.extract_elements(material_content)
            
            # 修正多媒体元素路径，确保都以output/项目名/开头
            project_name = os.path.basename(output_dir)
            corrected_media = []
            
            for media in multimedia_elements['important_media'][:10]:
                # 获取原始路径
                original_url = media.get('url', '')
                
                # 如果路径不是以output/开头，需要修正
                if original_url and not original_url.startswith('output/'):
                    # 检查是否是相对路径（如 media/xxx.png）
                    if not original_url.startswith(('http://', 'https://', '/')):
                        corrected_url = f"output/{project_name}/{original_url}"
                        media['url'] = corrected_url
                
                corrected_media.append(media)
            
            # 构建重要素材信息
            if corrected_media:
                important_media_info = "\n**重要多媒体素材**：\n"
                for i, media in enumerate(corrected_media):
                    media_type = media.get('category', 'other')
                    description = media.get('description', '无描述')
                    url = media.get('url', '')
                    important_media_info += f"{i+1}. {description} (类型: {media_type})\n"
                    if url:
                        important_media_info += f"   路径: {url}\n"
            
            # 截断材料内容，避免超长
            material_content = material_content[:100000]
        
        # 3. 构建生成提示
        generation_prompt = f"""
请根据用户目的优化以下材料内容，生成结构化的视频讲解稿。

**用户目的**: {purpose}
**目标内容长度**: 约{target_length}字符（根据视频时长自动计算）

**原始材料**:
{material_content}

**重要素材**：
{important_media_info}

**扩充素材状态**: 已自动扩充 {enhancement_result.get('enhancement_count', 0)} 项内容

**生成要求**:

1. **长度控制**：
   - 生成的markdown内容应控制在{target_length}字符左右（误差±20%可接受）
   - 根据目标长度调整内容详细程度：短视频重点突出，长视频可以详细展开
   - 合理分配各部分篇幅，确保重点内容有足够空间

2. **自适应内容结构**：根据用户目的自动决定：
   - 讲解提纲和重点章节
   - 内容详细程度和讲解深度  
   - 适合的表达方式和语言风格
   - 素材的选用和展示优先级
   - 可以参考原素材的讲解逻辑和重点

3. **重要素材优先**：
   - 优先使用演示Demo、架构图、流程图等核心素材
   - 在合适的章节位置插入重要多媒体内容
   - 为重要素材提供详细的讲解说明
   - 引用的素材链接必须使用完整路径，格式为：output/项目名/xxx，例如 ![架构图](output/{os.path.basename(output_dir)}/media/architecture.png)
   - 所有多媒体素材路径都必须以 output/{os.path.basename(output_dir)}/ 开头
   - 如果输入没有重要素材，输出markdown文件不要生造出来引用

4. **核心内容保留**：
   - 必须保留原材料中的核心认知、关键结论、重要发现
   - 不能遗漏关键技术点和创新之处
   - 保持原有逻辑结构的合理性

5. **精炼表达**：
   - 内容精炼不冗余，但要包含必要细节
   - 输出markdown文档的逻辑一定符合用户目的，逻辑清晰，不能跳跃性，不能缺失，比如一般论文适合"工作核心价值-核心架构创新-实验效果-批判性总结这类结构
   - 文案表述减少虚无、套话，都用实在的表述，不要浪费一个字，要言之有物

6. **特殊处理规则**：
   - GitHub项目：开篇突出项目热度和Stars数，重点展示Demo效果,Readme里核心能力介绍要完整，不要遗漏
   - 学术论文：开篇要强调研究机构（从作者信息中提取）、实验效果（从实验数据中提取）等关键信息，但不要过度夸大，要客观真实
   - 除以上两种类型，根据内容特点,按Github项目和学术论文例子，突出素材与其他类型不同的要素，自适应处理

**输出格式**：
```markdown
# [引人注目的标题]

## 内容概览
- **核心主题**：[主题描述]
- **目标受众**：[自动判断的目标受众]
- **主要收获**：[观众能获得的核心价值]
- **时间长度**：[视频时间长度]

## [第一部分标题]
[使用重要素材的开篇内容]

## [第二部分标题] 
[核心内容讲解，适当插入重要素材]

## [第三部分标题]
[深入分析或实践应用]

## 关键要点总结
[核心认知和关键结论的提炼]
```

请直接输出优化后的markdown内容，不要包含任何解释说明。
"""
        
        # 4. 使用生成代理创建内容
        from camel.messages import BaseMessage
        generated_content = self.agents["generator"].step(BaseMessage.make_user_message(
            role_name="User", content=generation_prompt
        )).msg.content
        
        # 5. 使用审查代理优化内容
        review_prompt = f"""
请审查并优化以下内容，确保：

1. **长度适配**：内容长度应控制在{target_length}字符左右，根据视频时长需求调整详细程度
2. **结构完整性**：标题层次清晰，内容逻辑合理
3. **核心价值突出**：重要认知和关键结论得到充分体现
4. **素材使用恰当**：重要多媒体素材在合适位置发挥作用
5. **表达精炼准确**：语言流畅，重点突出，易于理解,不要浪费一个字，要言之有物，不要使用虚无的表述
6. **用户目的达成**：内容符合用户的具体需求和目标
7. **素材引用正确**：
   - 检查每一个素材链接，确保路径格式为 output/{os.path.basename(output_dir)}/xxx
   - 去掉不在以下重要素材中的链接
   - 确保所有图片、视频等多媒体链接都以 output/{os.path.basename(output_dir)}/ 开头
   - 如果是论文，研究机构一定要包含，实验效果是否包含

**可用的重要素材列表**：
{important_media_info}

原始内容：
{generated_content}

请直接输出优化后的最终内容，不要包含审查说明。
"""
        
        final_content = self.agents["reviewer"].step(BaseMessage.make_user_message(
            role_name="User", content=review_prompt
        )).msg.content
        
        # 6. 添加扩充的介绍内容到开篇
        if intro_content:
            final_content = intro_content + final_content
        
        return final_content
    
    def save_material(self, content: str, output_file: str = None) -> str:
        """保存生成的材料"""
        if not output_file:
            output_file = f"output/optimized_material_{int(__import__('time').time())}.md"
        
        CommonUtils.save_file(content, output_file)
        return output_file
    
    def run(self, material_path: str, purpose: str, output_file: str = None) -> Dict[str, Any]:
        """主运行方法 - 简化的完整流程"""
        try:
            logger.info(f"开始处理材料: {material_path}")
            logger.info(f"用户目的: {purpose}")
            
            # 1. 读取材料
            material_content = self.read_material(material_path)
            if not material_content:
                logger.info("材料内容为空，启动chat模式")

            # 2. 确定输出目录
            if output_file:
                output_dir = os.path.dirname(output_file)
            else:
                # 如果有输入文件，使用其名称作为项目名
                if material_path:
                    project_name = os.path.splitext(os.path.basename(material_path))[0]
                else:
                    project_name = f"chat_material_{int(__import__('time').time())}"
                output_dir = f"output/{project_name}"

            # 3. 生成优化内容（包含素材扩充）
            optimized_content = self.generate_material(material_content, purpose, output_dir)
            
            # 4. 保存结果
            saved_file = self.save_material(optimized_content, output_file)
            
            # 5. 返回结果
            return {
                'status': 'success',
                'input_file': material_path,
                'output_file': saved_file,
                'output_dir': output_dir,
                'purpose': purpose,
                'content_length': len(optimized_content),
                'message': '材料优化完成（支持多种素材扩充）'
            }
            
        except Exception as e:
            logger.error(f"处理失败: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'message': '材料处理失败'
            }

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="简化版材料优化工具（支持多种素材扩充）")
    parser.add_argument("--material", "-m", type=str, help="输入材料文件路径")
    parser.add_argument("--purpose", "-p", type=str, help="用户目的描述")
    parser.add_argument("--output", "-o", type=str, help="输出文件路径")
    parser.add_argument("--config", "-c", type=str, default="config/config.yaml", help="配置文件路径")
    
    # 扩充功能开关
    parser.add_argument("--enable-video", action="store_true", help="强制启用录屏扩充")
    parser.add_argument("--disable-video", action="store_true", help="禁用录屏扩充")
    parser.add_argument("--enable-image", action="store_true", help="启用图片生成扩充")
    parser.add_argument("--enable-audio", action="store_true", help="启用音频合成扩充")
    
    args = parser.parse_args()
    
    try:
        # 创建agent，传递config路径
        agent = MaterialAgent(args.config)
        
        # 只有在明确指定命令行参数时才覆盖config配置
        enhancement_override = None
        if args.enable_video or args.disable_video or args.enable_image or args.enable_audio:
            enhancement_override = {
                'video_recording': (args.enable_video or not args.disable_video) and not args.disable_video,
                'image_generation': args.enable_image,
                'audio_synthesis': args.enable_audio,
            }
            # 重新创建agent使用命令行覆盖的配置
            agent = MaterialAgent(args.config, enhancement_override)
        
        # 使用命令行参数或提供默认值
        material_path = args.material
        purpose = args.purpose
        output_file = args.output
        
        # 如果没有提供必要参数，给出提示和默认值
        if not purpose:
            print("❌ 错误: 请指定用户目的")
            print("使用方法: python material_agent_refactored.py -m <材料文件> -p '<目的描述>'")
            print("示例: python material_agent_refactored.py -m output/project_analysis.md -p '为技术爱好者制作5分钟视频介绍'")
            print("\n扩充选项:")
            print("  --enable-video     强制启用录屏扩充")
            print("  --disable-video    禁用录屏扩充")
            print("  --enable-image     启用图片生成扩充")
            print("  --enable-audio     启用音频合成扩充")
            print("\n配置说明: 默认从config.yaml读取扩充配置，命令行参数可覆盖")
            return 1
        
        # 检查输入文件是否存在（仅当提供了文件路径时）
        if material_path and not os.path.exists(material_path):
            print(f"❌ 错误: 输入文件不存在: {material_path}")
            return 1
        
        print(f"📖 输入材料: {material_path if material_path else '无（Chat模式）'}")
        print(f"🎯 用户目的: {purpose}")
        
        # 显示启用的扩充功能
        enhancement_config = agent.enhancer.config
        enabled_features = []
        if enhancement_config.get('video_recording'):
            enabled_features.append("录屏视频")
        if enhancement_config.get('image_generation'):
            enabled_features.append("图片生成")
        if enhancement_config.get('audio_synthesis'):
            enabled_features.append("音频合成")
        
        if enabled_features:
            print(f"🎬 已启用扩充: {', '.join(enabled_features)}")
        else:
            print("🎬 未启用任何扩充功能")
            
        if output_file:
            print(f"📝 输出文件: {output_file}")
        
        # 执行处理
        result = agent.run(material_path, purpose, output_file)
        
        if result['status'] == 'success':
            print(f"\n✅ 处理完成！")
            print(f"输入文件: {result['input_file']}")
            print(f"输出文件: {result['output_file']}")
            print(f"内容长度: {result['content_length']} 字符")
            print(f"功能特色: {result['message']}")
        else:
            print(f"\n❌ 处理失败: {result['error']}")
            return 1
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main()) 