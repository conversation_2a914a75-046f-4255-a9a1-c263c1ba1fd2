#!/usr/bin/env python3
"""
素材扩充模块 - 统一管理各种扩充策略
采用策略模式，支持录屏、图片生成、音频合成等多种扩充类型
"""

import logging
import os
import re
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any

# 配置日志
logger = logging.getLogger(__name__)

# 扩充策略接口
class EnhancementStrategy(ABC):
    """素材扩充策略基类"""
    
    @abstractmethod
    def can_enhance(self, content: str) -> bool:
        """判断是否可以进行此类扩充"""
        pass
    
    @abstractmethod
    def enhance(self, content: str, output_dir: str) -> Optional[Dict[str, Any]]:
        """执行扩充，返回扩充结果"""
        pass
    
    @abstractmethod
    def generate_intro(self, enhancement_result: Dict[str, Any]) -> str:
        """生成扩充内容的介绍文本"""
        pass

class VideoRecordingStrategy(EnhancementStrategy):
    """录屏扩充策略"""
    
    def __init__(self, config=None):
        self.config = config
        self._initialize_toolkits()
    
    def _initialize_toolkits(self):
        """初始化录屏工具包（延迟导入）"""
        self.github_toolkit = None
        self.arxiv_toolkit = None
        
        try:
            from tools.github_scroller_toolkit import GithubScrollerToolkit
            from tools.arxiv_recorder_toolkit import ArxivRecorderToolkit
            
            self.github_toolkit = GithubScrollerToolkit()
            self.arxiv_toolkit = ArxivRecorderToolkit()
            logger.info("录屏工具包初始化成功")
        except ImportError as e:
            logger.warning(f"录屏工具包导入失败: {e}")
    
    def can_enhance(self, content: str) -> bool:
        """检查是否启用录屏功能（从config读取）"""
        if not self.config:
            logger.info("⏭️ 未提供配置，跳过录屏功能")
            return False
        
        # 检查config中是否启用录屏
        material_enhance = self.config.get('material', {}).get('material_enhance', {})
        if not material_enhance.get('screen_record', False):
            logger.info("⏭️ 配置中未启用录屏功能")
            return False
        
        # 获取当前启用的源
        sources = self.config.get('material', {}).get('sources', {})
        active_source = None
        for source_type, source_config in sources.items():
            if source_config.get('enabled', False):
                active_source = source_config
                active_source['type'] = source_type
                break
        
        if not active_source:
            logger.info("⏭️ 未找到启用的材料源")
            return False
        
        # 检查是否有可录屏的URL
        url = active_source.get('url', '')
        if not url:
            logger.info("⏭️ 启用的材料源中没有URL配置")
            return False
        
        # 检查URL类型
        if url.startswith(('http://', 'https://')):
            logger.info(f"🎬 检测到可录屏URL配置，启用录屏功能: {url}")
            return True
        
        logger.info("⏭️ URL格式不支持录屏")
        return False
    
    def enhance(self, content: str, output_dir: str) -> Optional[Dict[str, Any]]:
        """执行录屏扩充（从config获取URL）"""
        if not self.can_enhance(content):
            return None
        
        # 从config获取启用的源和URL
        sources = self.config.get('material', {}).get('sources', {})
        active_source = None
        for source_type, source_config in sources.items():
            if source_config.get('enabled', False):
                active_source = source_config
                active_source['type'] = source_type
                break
        
        if not active_source:
            return None
        
        detected_url = active_source.get('url', '')
        if not detected_url:
            return None
        
        # 确定源类型
        source_type = 'github'  # 默认使用github录屏方式
        if 'github.com' in detected_url:
            source_type = 'github'
        elif 'arxiv.org' in detected_url:
            source_type = 'arxiv'
            # 转换ArXiv PDF链接为页面链接
            if '/pdf/' in detected_url:
                detected_url = detected_url.replace('/pdf/', '/abs/')
        else:
            # 对于其他类型的网页，默认使用github录屏方式
            source_type = 'github'
            logger.info(f"🎬 使用默认GitHub录屏方式处理: {detected_url}")
        
        # 生成输出路径
        video_filename = "screen_record.mp4"
        video_path = os.path.join(output_dir, video_filename)
        
        # 确保目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        # 检查是否已存在文件
        if os.path.exists(video_path):
            logger.info(f"录屏视频已存在: {video_path}")
            return {
                'type': 'video_recording',
                'source_type': source_type,
                'url': detected_url,
                'file_path': video_path,
                'full_path': video_path,
                'status': 'exists'
            }
        
        # 执行录屏
        try:
            result = self._record_video(detected_url, video_path, source_type)
            if result and result.get("status") == "success":
                return {
                    'type': 'video_recording',
                    'source_type': source_type,
                    'url': detected_url,
                    'file_path': video_path,
                    'full_path': video_path,
                    'status': 'created'
                }
        except Exception as e:
            logger.error(f"录屏过程异常: {e}")
        
        return None
    
    def _record_video(self, url: str, output_path: str, source_type: str) -> Optional[Dict]:
        """执行实际的录屏操作"""
        if source_type == 'github' and self.github_toolkit:
            return self.github_toolkit.record_github_scroll_video(
                url=url, output_path=output_path,
                duration=12, width=1920, height=1080, fps=15,
                smooth_factor=0.2, title_focus=1, star_focus=2,
                zoom_factor=2.0, readme_pause=1.0
            )
        elif source_type == 'arxiv' and self.arxiv_toolkit:
            return self.arxiv_toolkit.record_arxiv_video(
                url=url, output_path=output_path,
                duration=8, width=1920, height=1080, fps=15,
                smooth_factor=0.2, title_focus=4,
                zoom_factor=2.0, abstract_pause=4.0
            )
        return None
    
    def generate_intro(self, enhancement_result: Dict[str, Any]) -> str:
        """生成录屏视频的介绍内容"""
        source_type = enhancement_result.get('source_type')
        url = enhancement_result.get('url')
        file_path = enhancement_result.get('file_path')
        
        if source_type == 'github':
            # 提取GitHub项目信息
            match = re.search(r'github\.com/([\w\-]+)/([\w\-]+)', url)
            if match:
                owner, repo = match.groups()
                intro = f"# {repo} 项目概览\n\n"
                intro += f"这是 **{owner}/{repo}** 项目的整体情况，"
                intro += "通过视频可以快速了解项目的基本信息和特点。\n\n"
                intro += f"[项目介绍视频]({file_path})\n\n"
            else:
                intro = f"# 项目概览视频\n\n[查看项目介绍]({file_path})\n\n"
        elif source_type == 'arxiv':
            # 提取ArXiv论文信息
            match = re.search(r'(\d{4}\.\d{5})', url)
            if match:
                paper_id = match.group(1)
                intro = f"# 论文 {paper_id} 页面浏览\n\n"
                intro += "这是论文整体信息"
                intro += "可以通过视频快速了解论文的标题、摘要和基本信息。\n\n"
                intro += f"[论文页面视频]({file_path})\n\n"
            else:
                intro = f"# 论文页面浏览\n\n[查看论文介绍]({file_path})\n\n"
        else:
            intro = f"# 内容概览视频\n\n[查看内容介绍]({file_path})\n\n"
        
        return intro

class ImageGenerationStrategy(EnhancementStrategy):
    """图片生成扩充策略 - 示例实现"""
    
    def can_enhance(self, content: str) -> bool:
        """检查是否需要生成图片（简化判断）"""
        # 简化的判断逻辑：检查是否包含图片需求关键词
        keywords = ['架构图', '流程图', '示意图', '概念图', '图解', '可视化']
        return any(keyword in content for keyword in keywords)
    
    def enhance(self, content: str, output_dir: str) -> Optional[Dict[str, Any]]:
        """执行图片生成扩充（示例实现）"""
        if not self.can_enhance(content):
            return None
        
        # 这里是示例代码，实际使用时需要接入真正的图片生成API
        logger.info("🎨 检测到图片生成需求，准备生成概念图")
        
        # 生成输出路径
        image_filename = "generated_concept.png"
        image_path = os.path.join(output_dir, image_filename)
        os.makedirs(output_dir, exist_ok=True)
        
        # 模拟图片生成过程
        try:
            # TODO: 这里应该调用实际的图片生成API
            # result = image_generation_api.generate(prompt=content[:500])
            
            # 目前只是创建一个占位文件
            if not os.path.exists(image_path):
                with open(image_path, 'w') as f:
                    f.write("# 这是一个图片生成占位符\n# 实际使用时会生成真实图片")
                logger.info(f"🎨 概念图生成完成: {image_path}")
            
            return {
                'type': 'image_generation',
                'file_path': image_path,
                'description': '自动生成的概念图',
                'status': 'generated'
            }
            
        except Exception as e:
            logger.error(f"图片生成失败: {e}")
            return None
    
    def generate_intro(self, enhancement_result: Dict[str, Any]) -> str:
        """生成图片的介绍内容"""
        file_path = enhancement_result.get('file_path')
        description = enhancement_result.get('description', '概念图')
        
        intro = f"## 📊 {description}\n\n"
        intro += f"基于内容自动生成的可视化图表，帮助更好地理解核心概念。\n\n"
        intro += f"![{description}]({file_path})\n\n"
        
        return intro

class AudioSynthesisStrategy(EnhancementStrategy):
    """音频合成扩充策略 - 示例实现"""
    
    def can_enhance(self, content: str) -> bool:
        """检查是否需要生成音频（简化判断）"""
        # 简化判断：内容长度适合语音合成
        return len(content) > 200 and len(content) < 10000
    
    def enhance(self, content: str, output_dir: str) -> Optional[Dict[str, Any]]:
        """执行音频合成扩充（示例实现）"""
        if not self.can_enhance(content):
            return None
        
        logger.info("🎵 检测到音频合成需求，准备生成讲解音频")
        
        # 生成输出路径
        audio_filename = "narration_audio.mp3"
        audio_path = os.path.join(output_dir, audio_filename)
        os.makedirs(output_dir, exist_ok=True)
        
        try:
            # TODO: 这里应该调用实际的语音合成API
            # result = tts_api.synthesize(text=content[:2000])
            
            # 目前只是创建一个占位文件
            if not os.path.exists(audio_path):
                with open(audio_path, 'w') as f:
                    f.write("# 这是一个音频合成占位符\n# 实际使用时会生成真实音频文件")
                logger.info(f"🎵 讲解音频生成完成: {audio_path}")
            
            return {
                'type': 'audio_synthesis',
                'file_path': audio_path,
                'description': '自动生成的讲解音频',
                'duration': '约3-5分钟',
                'status': 'generated'
            }
            
        except Exception as e:
            logger.error(f"音频合成失败: {e}")
            return None
    
    def generate_intro(self, enhancement_result: Dict[str, Any]) -> str:
        """生成音频的介绍内容"""
        file_path = enhancement_result.get('file_path')
        description = enhancement_result.get('description', '讲解音频')
        duration = enhancement_result.get('duration', '未知时长')
        
        intro = f"## 🎵 {description}\n\n"
        intro += f"基于内容自动生成的语音讲解，时长{duration}。\n\n"
        intro += f"[收听讲解音频]({file_path})\n\n"
        
        return intro

class MaterialEnhancer:
    """素材扩充器 - 统一管理各种扩充策略"""
    
    def __init__(self, enhancement_config: Optional[Dict[str, bool]] = None, config_dict: Optional[Dict] = None):
        """
        初始化素材扩充器
        
        Args:
            enhancement_config: 扩充策略配置字典（命令行覆盖）
            config_dict: 完整配置字典（从config.yaml读取）
        """
        self.config_dict = config_dict or {}
        
        # 优先使用命令行配置，其次使用config文件配置
        if enhancement_config:
            self.config = enhancement_config
        else:
            # 从config文件读取扩充配置
            material_enhance = self.config_dict.get('material', {}).get('material_enhance', {})
            self.config = {
                'video_recording': material_enhance.get('screen_record', False),
                'image_generation': material_enhance.get('image_generation', False),
                'audio_synthesis': material_enhance.get('audio_synthesis', False),
            }
        
        self.strategies = self._initialize_strategies()
    
    def _initialize_strategies(self) -> List[EnhancementStrategy]:
        """根据配置初始化策略列表"""
        strategies = []
        
        if self.config.get('video_recording', False):
            strategies.append(VideoRecordingStrategy(self.config_dict))
            logger.info("✅ 已启用录屏扩充策略")
        
        if self.config.get('image_generation', False):
            strategies.append(ImageGenerationStrategy())
            logger.info("✅ 已启用图片生成扩充策略")
        
        if self.config.get('audio_synthesis', False):
            strategies.append(AudioSynthesisStrategy())
            logger.info("✅ 已启用音频合成扩充策略")
        
        if not strategies:
            logger.info("⚠️ 未启用任何扩充策略")
        
        return strategies
    
    def add_strategy(self, strategy: EnhancementStrategy, strategy_name: str = None):
        """动态添加新的扩充策略"""
        self.strategies.append(strategy)
        name = strategy_name or strategy.__class__.__name__
        logger.info(f"✅ 已添加扩充策略: {name}")
    
    def remove_strategy(self, strategy_class):
        """移除指定类型的策略"""
        original_count = len(self.strategies)
        self.strategies = [s for s in self.strategies if not isinstance(s, strategy_class)]
        removed_count = original_count - len(self.strategies)
        if removed_count > 0:
            logger.info(f"✅ 已移除 {removed_count} 个 {strategy_class.__name__} 策略")
    
    def enhance_material(self, content: str, output_dir: str = "output") -> Dict[str, Any]:
        """执行素材扩充，返回扩充结果"""
        logger.info("🎬 开始素材扩充步骤")
        
        if not self.strategies:
            logger.info("⚠️ 无可用的扩充策略，跳过扩充步骤")
            return {
                'intro_content': '',
                'enhancements': [],
                'enhancement_count': 0
            }
        
        enhancements = []
        intro_parts = []
        
        # 遍历所有策略
        for strategy in self.strategies:
            try:
                if strategy.can_enhance(content):
                    logger.info(f"执行扩充策略: {strategy.__class__.__name__}")
                    result = strategy.enhance(content, output_dir)
                    
                    if result:
                        enhancements.append(result)
                        intro = strategy.generate_intro(result)
                        if intro:
                            intro_parts.append(intro)
                        logger.info(f"✅ {strategy.__class__.__name__} 扩充成功")
                    else:
                        logger.info(f"⚠️ {strategy.__class__.__name__} 扩充未成功")
                else:
                    logger.info(f"⏭️ {strategy.__class__.__name__} 不适用于当前内容")
                        
            except Exception as e:
                logger.error(f"扩充策略 {strategy.__class__.__name__} 执行异常: {e}")
        
        # 合并所有介绍内容
        combined_intro = "".join(intro_parts)
        
        result = {
            'intro_content': combined_intro,
            'enhancements': enhancements,
            'enhancement_count': len(enhancements),
            'strategies_used': [e.get('type') for e in enhancements]
        }
        
        logger.info(f"🎬 素材扩充完成，成功扩充 {len(enhancements)} 项内容")
        return result

# 便捷的扩充策略注册器
class EnhancementRegistry:
    """扩充策略注册器 - 方便动态管理策略"""
    
    _strategies = {
        'video_recording': VideoRecordingStrategy,
        'image_generation': ImageGenerationStrategy,
        'audio_synthesis': AudioSynthesisStrategy,
    }
    
    @classmethod
    def register_strategy(cls, name: str, strategy_class):
        """注册新的策略类"""
        cls._strategies[name] = strategy_class
        logger.info(f"📝 已注册新策略: {name}")
    
    @classmethod
    def get_strategy_class(cls, name: str):
        """获取策略类"""
        return cls._strategies.get(name)
    
    @classmethod
    def list_strategies(cls) -> List[str]:
        """列出所有可用策略"""
        return list(cls._strategies.keys())
    
    @classmethod
    def create_enhancer_from_names(cls, strategy_names: List[str]) -> MaterialEnhancer:
        """从策略名称列表创建扩充器"""
        config = {name: True for name in strategy_names if name in cls._strategies}
        return MaterialEnhancer(config) 