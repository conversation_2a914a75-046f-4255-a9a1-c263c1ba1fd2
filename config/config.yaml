# Feynman Workflow Configuration
# Model Configuration
model:
  platform: "openrouter"
  type: "google/gemini-2.5-flash-preview-05-20"
  #type: "google/gemini-2.5-pro-preview"
  temperature: 0.7
  max_tokens: 999_999_999
  # API配置
  api:
    openai_compatibility_api_base_url: "https://openrouter.ai/api/v1"
    openai_compatibility_api_key: "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"
    openrouter_api_base_url: "https://openrouter.ai/api/v1"
    openrouter_api_key: "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"

# File Configuration
files:
  outline_file: "output/outline.json"
  storyboard_file: "output/storyboard.json"
  content_file: "output/paper_content.json"
  paper_discussion_file: "output/paper_discussion.json"
  feynman_explanation_file: "output/feynman_explanation.json"
  feynman_full_text_file: "output/feynman_explanation_full.txt"
  output_dir: "output"

# Material Agent Configuration - 统一的材料源管理
material:
  # 材料源配置 - 同时只能启用一个
  sources:
    github:
      enabled: false  # 启用GitHub项目分析
      url: "https://github.com/google-gemini/gemini-fullstack-langgraph-quickstart"
      purpose: "给技术爱好者介绍这个AI项目，目的介绍项目价值、具体核心能力等，风格是应用性，吸引用户关注试用，视频4分钟"

      # GitHub分析配置
      analysis_config:
        deep_analysis: false  # 是否启用深度代码分析
        download_media: true  # 是否下载多媒体资源
        auto_clone: true  # 是否自动克隆仓库
        max_files: 200  # 最大分析文件数量
        max_content_size_kb: 1024  # 最大内容大小(KB)

    pdf:
      enabled: false # 启用PDF文档分析
      url: "https://arxiv.org/pdf/2505.24863"
      purpose: "为研究人员介绍这篇论文，目的是介绍工作价值，核心能力，关键概念，实验效果等，风格具备批判性看，视频3分钟"
      #purpose: "为研究人员介绍这篇论文，目的是介绍工作价值，核心能力，关键概念，重要认知观点等，风格具备批判性看，视频3分钟"
      #url: "https://arxiv.org/pdf/2401.03568"
      #purpose: "为研究人员和学生介绍这篇综述论文，目的是介绍这个领域的研究体系和方向，重要的工作、以及未来趋势，视频15分钟"
      #url: "https://arxiv.org/pdf/2504.07491"
      #purpose: "给工程技术人员介绍这篇技术报告论文，目的是介绍这个技术的创新性，核心能力，经验技巧，风格偏应用和经验分享，视频10分钟"


    webpage:
      enabled: true  # 启用网页内容分析
      url: "https://zh.wikipedia.org/wiki/宋朝"  # 示例：OpenAI GPT-4o 介绍页面
      purpose: "分析网页内容，为读者提供核心信息的总结和见解，视频2分钟"

      # 网页提取配置 (可选)
      extract_config:
        timeout: 30  # 请求超时时间(秒)
        download_images: true  # 是否下载图片
        image_filter:
          skip_keywords: ['badge', 'icon', 'logo', 'license', 'button']  # 跳过的图片类型
          min_size_kb: 5  # 最小图片大小(KB)
        user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"

    local_file:
      enabled: false  # 启用本地文件分析
      path: "./output/非同寻常的树懒.pdf"
      file_type: "pdf"  # 支持 markdown, txt, docx, pdf 等
      purpose: "给幼儿介绍这边绘本故事，目的介绍故事内容，风格轻松活泼，视频长度5分钟"
      #purpose: "分析这本书，提炼核心思想，目的给家长贩卖焦虑，购买这本书，风格营销感要强，视频长度5分钟"

    chat:
      enabled: false  # 启用chat模式
      purpose: "给大学生讲解Transformer算法的核心原理，风格要通俗易懂便于记忆，视频长度5分钟"  # 要解释的主题

  # 素材生成配置
  max_rounds: 1
  output_dir: "output"
  material_file: "output/video_material.md"
  generator_enabled: true  # 控制素材生成器是否启用
  analysis_techniques:
    - "audience_focus"
    - "content_refinement"
    - "style_adaptation"
    - "multimedia_handling"
  # 默认视频长度
  default_video_length: "2分钟"
  # 默认目的描述
  default_purpose: "提供简单易懂的内容分析，面向一般受众，以客观中立的风格呈现"
  # 目标受众群体预设
  audience_presets:
    research: "研究人员"
    student: "学生"
    tech_enthusiast: "技术爱好者"
    general: "一般受众"
  # 风格预设
  style_presets:
    academic: "学术严谨"
    casual: "通俗易懂"
    objective: "客观中立"
    enthusiastic: "热情活力"

  # 素材扩充配置
  material_enhance:
    screen_record: true  # 是否启用录屏功能，从配置中的URL自动录屏
    image_generation: false  # 是否启用图片生成
    audio_synthesis: false  # 是否启用音频合成
    timeline_generation: true

# Agent Configuration
agents:
  source: False
  outline_generator: True
  outline_reflector: True
  storyboard_generator: True
  storyboard_reflector: True
  content_generator: False
  material_generator: True

# 大纲生成配置
outline:
  max_rounds: 3
  query_template: "我想了解这篇文章: {url}"
  user_profile:
    education_level: "研究生"
    field: "计算机科学"
    interests:
      - "人工智能"
      - "机器学习"
      - "自然语言处理"
    expertise_level: "中级"
    content_preferences:
      style: "通俗易懂"
      depth: "中等"
      format: "视频讲解"
  content_topics:
    - "论文解读"
    - "学术分享"
    - "技术讲解"

# Feynman Agent Configuration
feynman:
  article_file: "output/paper_content.md"
  storyboard_file: "output/storyboard.json"
  max_rounds: 3
  explanation_techniques:
    - "simple_language"
    - "daily_analogy"
    - "code_examples"
    - "data_analysis"
    - "interactive_exercises"
    - "metaphors"
    - "visualization"
  output_format: "json"

# 工作流控制配置
workflow:
  # 启用或禁用大纲生成模块
  enable_outline: true
  # 启用或禁用费曼解释模块
  enable_feynman: true
  # 启用或禁用素材规范化模块
  enable_material: true
  # 从哪个阶段开始执行
  start_stage: null
  # 最大修改尝试次数
  max_revision_attempts: 2
  # 各阶段结果文件路径
  intention_file: "output/intention_result.json"
  paper_info_file: "output/paper_info.json"
  paper_content_file: "output/paper_content.md"
  outline_file: "output/outline_result.json"
  discussion_file: "output/discussion_result.json"
  feynman_file: "output/feynman_result.json"
  material_file: "output/material_result.json"
  generation_file: "output/generated_content.json"
  media_fix_file: "output/fixed_content.json"
  evaluation_file: "output/evaluation_result.json"
  revision_file: "output/revised_content.json"
  # RAG和视觉修复配置
  enable_rag: true
  enable_context_learning: true
  enable_visual_fix: true
  max_fix_attempts: 3
  chroma_db_path: "data/rag/chroma_db"
  manim_docs_path: "data/rag/manim_docs"
  embedding_model: "text-embedding-3-large"
  scene_plan_file: "output/{topic}/scene_outline.txt"
  vision_storyboard_dir: "output/{topic}/vision_storyboard"
  technical_implementation_dir: "output/{topic}/technical_implementation"
  animation_narration_dir: "output/{topic}/animation_narration"
  code_dir: "output/{topic}/code"
  video_dir: "output/{topic}/videos"

# Manim Configuration
manim:
  quality: "h"
  width: 1920
  height: 1080
  background_color: "#000000"

# 全局配置
app:
  name: "Feynman-AI"
  version: "0.2.0"
  environment: "development"

# API配置
api:
  openai:
    api_key: ${OPENAI_API_KEY}
    base_url: ${OPENAI_API_BASE}
    organization: ${OPENAI_ORG_ID}
    default_model: "gpt-4-turbo-preview"
    max_retries: 3
    timeout: 60

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/app.log"
  max_size: 10485760  # 10MB
  backup_count: 5

code_generation:
  repo_url: "https://github.com/camel-ai"
  repo_path: "/Users/<USER>/Documents/git/camel"
  prompt: "写一个agent收集近一周的经济事件并分析对美股和A股的影响，他通过openBB工具获取相关数据。"
  reference_files: ["output/camels/code_refer.py", "output/camels/openBB_help.txt"]
  output_md: "output/camels/camel_compare_code_info.md"
  output_py: "output/camels/camel_compare_code.py"
  video_file: "output/camels/camel_compare_video.mp4"

theme:
  name: "light"
