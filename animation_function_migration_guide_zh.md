# 动画函数迁移指南

本指南概述了将现有动画逻辑（例如，来自访问者模式的逻辑）重构为 `dsl/v2/animation_functions/` 目录下的专用动画函数，并将其集成到 `FeynmanScene` 中的步骤。本指南基于已成功迁移的多个动画函数（如图表、计数器、时间轴和高亮序列等）的实际经验。

**阶段一：分析与设计**

1.  **理解现有功能：**
    *   **定位旧代码：** 
        *   找到动画的当前实现（例如，访问者类中的 `visit_...` 方法，或旧的独立函数）。
        *   例如，从 `dsl/v2/visitors/timeline.py` 中的 `visit_timeline` 函数迁移到 `dsl/v2/animation_functions/animate_timeline.py`。

    *   **分析逻辑：**
        *   **输入参数：** 确定函数需要哪些参数，如何将它们从旧的访问者模式转换为新的函数参数。
        *   **Mobject 创建：** 理解它如何创建和操作 Manim 的 mobject（如 `Text`、`Circle`、`Line` 等）。
        *   **动画序列：** 注意动画的顺序（`play`、`wait`、过渡效果）。
        *   **区域管理：** 识别它如何与场景区域交互或管理其显示区域。
        *   **依赖关系：** 列出它依赖的任何辅助函数或特定的 Manim 功能。
        *   **错误处理：** 注意现有代码如何处理边缘情况和错误。

2.  **设计新的动画函数：**
    *   **文件命名：**
        *   在 `dsl/v2/animation_functions/` 中创建一个新的 Python 文件。
        *   使用描述性名称，例如 `animate_chart.py`、`animate_timeline.py`、`highlight_sequence.py` 等。
        *   命名应当反映功能而非实现方式，如 `animate_counter.py` 而非 `animate_decimal.py`。

    *   **数据结构设计：**
        *   如果需要，创建专用的数据类来表示复杂的输入数据。
        *   例如，在 `animate_timeline.py` 中使用 `@dataclass` 创建 `TimelineEvent` 类：
        ```python
        @dataclass
        class TimelineEvent:
            """表示时间轴上的事件。"""
            time: str
            text: Optional[str] = None
            image: Optional[str] = None
            annotation: Optional[str] = None
        ```

    *   **函数签名：**
        *   定义主动画函数，例如 `def animate_timeline(...)`。
        *   第一个参数必须是 `scene: FeynmanScene`。
        *   使用现代 Python 类型提示，如 `list[dict[str, Any]]` 而非旧式的 `List[Dict[str, Any]]`。
        *   保持参数命名一致性，常见参数包括：
            * `target_region_id: str = "full_screen"` - 显示区域
            * `narration: Optional[str] = None` - 旁白文本
            * `id: Optional[str] = None` - 唯一标识符
            * `transition_in: str = "fadeIn"` - 过渡效果
        *   为所有参数提供合理的默认值，减少用户配置负担。
        *   使用 `**kwargs: Any` 参数来支持未来可能的扩展。

    *   **辅助函数设计：**
        *   将复杂逻辑分解为私有辅助函数，使主函数更清晰。
        *   使用前缀下划线（如 `_create_bar_chart`）标记私有辅助函数。
        *   为每个辅助函数提供清晰的文档字符串，说明其目的和参数。
        *   示例：
        ```python
        def _create_bar_chart(
            scene: FeynmanScene,
            normalized_data: list[dict],
            labels: list[str],
            colors: list[str],
            options: dict[str, Any] = None,
        ) -> VGroup:
            """创建包含多个数据集的条形图。"""
            # 实现...
        ```

**阶段二：实现**

3.  **实现核心动画逻辑：**
    *   **导入与代码风格：**
        *   在文件顶部添加必要的导入，保持整洁：
        ```python
        from typing import Any, Optional
        import numpy as np
        from loguru import logger
        from manim import *
        
        from dsl.v2.core.scene import FeynmanScene
        ```
        *   遵循 PEP 8 代码风格，使用一致的缩进和命名约定。
        *   使用 Black 或类似工具格式化代码，确保一致性。
        *   对于字符串引号，优先使用双引号（`"`）而非单引号。

    *   **文档字符串：**
        *   为主函数和所有辅助函数提供详细的文档字符串。
        *   包括函数目的、参数说明和返回值说明。
        *   示例：
        ```python
        def animate_chart(
            scene: FeynmanScene,
            chart_type: str,
            data: Any,
            title: Optional[str] = None,
            animation_style: str = "grow",
            target_region_id: str = "full_screen",
            narration: Optional[str] = None,
            id: Optional[str] = None,
            dataset_names: Optional[list[str]] = None,
            options: Optional[dict[str, Any]] = None,
        ) -> None:
            """在指定区域创建并动画展示图表。
            
            Args:
                scene: FeynmanScene 实例
                chart_type: 图表类型 ('bar', 'line', 或 'radar')
                data: 图表数据，可以是单个字典或字典列表
                title: 可选的图表标题
                animation_style: 动画风格 ('grow', 'fadeIn', 'draw', 或 'update')
                target_region_id: 显示图表的区域 ID
                narration: 可选的旁白文本
                id: 可选的图表唯一标识符
                dataset_names: 数据集名称列表，用于图例
                options: 图表选项字典，如轴标签、颜色等
            """
        ```

    *   **日志记录：**
        *   使用 `loguru` 在关键点添加日志记录：
        ```python
        logger.info(f"在区域 {target_region_id} 中动画化图表...")
        # ...
        logger.info(f"完成动画化图表 '{unique_id}'")
        ```
        *   记录警告和错误，帮助调试：
        ```python
        if not os.path.exists(image_path):
            logger.error(f"图像文件未找到: {image_path}")
        ```

    *   **标准化数据处理：**
        *   创建辅助函数来标准化输入数据格式：
        ```python
        def _normalize_data(data: Any) -> tuple[list[dict], list[str]]:
            """将输入数据标准化为统一格式。"""
            # 实现...
        ```
        *   处理各种输入格式，使主函数更简洁。

    *   **区域管理：**
        *   清理目标区域，准备显示新内容：
        ```python
        scene.region_manager.clear_region(target_region_id)
        ```
        *   生成唯一标识符，用于区域内容管理：
        ```python
        unique_id = id or f"chart_{abs(hash(str(data))) % 10000}"
        ```
        *   在动画结束时更新区域内容：
        ```python
        scene.region_manager.update_region_content(target_region_id, group, unique_id)
        ```

    *   **Mobject 创建与布局：**
        *   将复杂的 Mobject 创建逻辑分解为辅助函数。
        *   确保所有元素都有适当的定位和缩放。
        *   使用 `Group` 或 `VGroup` 组合相关元素。
        *   示例（创建图例）：
        ```python
        def _create_legend(
            scene: FeynmanScene,
            dataset_names: list[str],
            colors: list[str],
        ) -> VGroup:
            """为多个数据集创建图例。"""
            legend_items = []
            for i, name in enumerate(dataset_names):
                color_rect = Rectangle(height=0.2, width=0.4, fill_opacity=1, fill_color=colors[i], stroke_width=1)
                label = Text(name, font_size=18, font="Maple Mono NF CN")
                label.next_to(color_rect, RIGHT, buff=0.1)
                legend_item = VGroup(color_rect, label)
                legend_items.append(legend_item)
            
            # 排列图例项
            legend = VGroup()
            for i, item in enumerate(legend_items):
                if i == 0:
                    legend.add(item)
                else:
                    item.next_to(legend, RIGHT, buff=0.3)
                    legend.add(item)
            
            return legend
        ```

    *   **自适应缩放：**
        *   实现自适应缩放，确保内容适合目标区域：
        ```python
        target_rect = scene.region_manager.get_region_rect(target_region_id)
        if target_rect:
            available_width = target_rect.width * 0.95
            available_height = target_rect.height * 0.95
            
            if group.width > 0 and group.height > 0:
                scale_factor = min(available_width / group.width, available_height / group.height)
                if scale_factor < 1.0:
                    group.scale(scale_factor)
                
                # 居中显示在目标区域
                group.move_to(target_rect.get_center())
        ```

    *   **动画与旁白协调：**
        *   使用 `voiceover` 上下文管理器处理旁白：
        ```python
        if narration:
            with scene.voiceover(text=narration) as tracker:
                narration_duration = tracker.duration if hasattr(tracker, "duration") else 0
                
                # 动画序列...
                
                # 等待旁白完成
                effective_duration = max(0, narration_duration - animation_duration)
                if effective_duration > 0:
                    scene.wait(effective_duration)
        else:
            # 无旁白的动画序列...
        ```

    *   **错误处理：**
        *   添加适当的错误检查和异常处理：
        ```python
        if chart_type not in ["bar", "line", "radar"]:
            logger.error(f"不支持的图表类型: {chart_type}")
            return
        
        try:
            img = ImageMobject(event.image)
        except Exception as e:
            logger.error(f"加载图像 {event.image} 失败: {e}")
        ```

4.  **测试函数实现：**
    *   **测试函数设计：**
        *   在每个动画函数文件末尾添加测试函数：
        ```python
        def test_animate_timeline():
            """时间轴动画的测试函数。"""
            from manim import config
            
            # 配置 Manim 进行测试
            config.quality = "low_quality"
            config.preview = True
            config.output_file = "TimelineAnimationTest.mp4"
            config.disable_caching = True
            
            class TestTimelineScene(FeynmanScene):
                def construct(self):
                    self.camera.background_color = "#1E1E1E"
                    
                    # 测试用例
                    events = [
                        TimelineEvent(time="2020", text="项目启动", annotation="初始阶段"),
                        TimelineEvent(time="2021", text="技术研发", annotation="核心技术突破"),
                        # ...
                    ]
                    
                    animate_timeline(
                        scene=self,
                        events=events,
                        focus_effect="flash",
                        duration=1.0,
                        narration="这是一个时间轴演示...",
                    )
                    
                    logger.info("测试场景构建完成。")
            
            # 创建并渲染测试场景
            scene_instance = TestTimelineScene()
            scene_instance.render()
        
        if __name__ == "__main__":
            test_animate_timeline()
        ```

    *   **多样化测试用例：**
        *   创建多个测试用例，覆盖不同参数组合。
        *   测试边缘情况，如空数据、极大/极小值等。
        *   测试不同的动画风格和过渡效果。

**阶段四：集成

5.  **与 `FeynmanScene` 集成：**
    *   **添加接口方法：**
        *   在 `dsl/v2/core/scene.py` 中向 `FeynmanScene` 类添加新方法：
        ```python
        def display_timeline(
            self,
            events: list[Any],
            focus_effect: str = "flash",
            duration: float = 1.0,
            timeline_color: str = "#6666FF",
            label_font: Optional[str] = None,
            event_style: Optional[dict[str, Any]] = None,
            target_region_id: str = "full_screen",
            narration: Optional[str] = None,
            id: Optional[str] = None,
            **kwargs: Any,
        ):
            """
            创建并动画展示时间轴。
            
            Args:
                events: 时间轴事件列表，每个事件可以是TimelineEvent对象或字典
                focus_effect: 聚焦效果 ('flash', 'zoom', 或 'color')
                duration: 每个事件的聚焦时间
                timeline_color: 时间轴线的颜色
                label_font: 标签和文本使用的字体
                event_style: 事件样式选项字典（shape, color）
                target_region_id: 显示时间轴的区域 ID
                narration: 可选的旁白文本
                id: 可选的时间轴唯一标识符
                **kwargs: 其他参数
            """
            # 本地导入以防止循环依赖
            from ..animation_functions.animate_timeline import animate_timeline as actual_animation_func
            
            actual_animation_func(
                scene=self,
                events=events,
                focus_effect=focus_effect,
                duration=duration,
                timeline_color=timeline_color,
                label_font=label_font,
                event_style=event_style,
                target_region_id=target_region_id,
                narration=narration,
                id=id,
                **kwargs,
            )
        ```

    *   **本地导入：**
        *   使用本地导入避免循环依赖问题。
        *   将实际动画函数重命名为 `actual_animation_func` 以区分。

    *   **参数传递：**
        *   确保所有参数都正确传递给实际的动画函数。
        *   保持参数名称一致，避免混淆。

    *   **文档字符串：**
        *   为接口方法提供详细的文档字符串，包括所有参数的说明。
        *   使用中文文档字符串，方便中文用户理解。

6.  **代码质量与风格：**
    *   **代码格式化：**
        *   使用一致的缩进（4个空格）。
        *   保持行长度合理（通常不超过100个字符）。
        *   使用空行分隔逻辑块，提高可读性。
        *   使用 Black 或类似工具自动格式化代码。

    *   **命名约定：**
        *   使用描述性的函数和变量名称。
        *   对于私有辅助函数，使用前缀下划线（如 `_create_legend`）。
        *   使用小写加下划线的命名风格（snake_case）。

    *   **类型提示：**
        *   使用现代 Python 类型提示语法（Python 3.9+）：
            * `list[str]` 而非 `List[str]`
            * `dict[str, Any]` 而非 `Dict[str, Any]`
        *   为所有函数参数和返回值添加类型提示。

    *   **字符串与引号：**
        *   优先使用双引号（`"`）而非单引号。
        *   对于文档字符串，使用三重双引号（`"""`）。

    *   **注释：**
        *   添加必要的注释解释复杂逻辑。
        *   避免过度注释明显的代码。
        *   使用行内注释说明特定行的目的。

7.  **常见问题与解决方案：**
    *   **循环依赖：**
        *   使用本地导入避免循环依赖问题。
        *   将导入语句放在函数内部，而非文件顶部。

    *   **区域管理：**
        *   始终在动画开始时清理目标区域。
        *   在动画结束时更新区域内容。
        *   使用唯一标识符跟踪区域内容。

    *   **动画与旁白同步：**
        *   使用 `voiceover` 上下文管理器处理旁白。
        *   计算有效等待时间，确保旁白完成。
        *   考虑动画持续时间与旁白持续时间的关系。

    *   **缩放与定位：**
        *   实现自适应缩放，确保内容适合目标区域。
        *   避免内容溢出屏幕或区域边界。
        *   考虑不同屏幕尺寸和比例。

    *   **错误处理：**
        *   添加适当的错误检查和异常处理。
        *   记录警告和错误，帮助调试。
        *   提供有用的错误消息，指导用户解决问题。

8.  **示例与模板：**
    *   **基本动画函数模板：**
        ```python
        def animate_feature(
            scene: FeynmanScene,
            # 必要参数
            param1: str,
            # 可选参数
            param2: int = 10,
            target_region_id: str = "full_screen",
            narration: Optional[str] = None,
            id: Optional[str] = None,
            **kwargs: Any,
        ) -> None:
            """函数文档字符串。"""
            logger.info(f"在区域 {target_region_id} 中动画化 Feature...")
            scene.region_manager.clear_region(target_region_id)
            
            # 生成唯一标识符
            unique_id = id or f"feature_{abs(hash(param1)) % 10000}"
            
            # 创建 Mobject
            # ...
            
            # 定位与缩放
            target_rect = scene.region_manager.get_region_rect(target_region_id)
            if target_rect:
                # 缩放逻辑...
            
            # 动画序列
            if narration:
                with scene.voiceover(text=narration) as tracker:
                    # 动画逻辑...
            else:
                # 无旁白的动画逻辑...
            
            # 更新区域内容
            scene.region_manager.update_region_content(target_region_id, mobject, unique_id)
            logger.info(f"完成动画化 Feature '{unique_id}'")
        ```

    *   **FeynmanScene 集成模板：**
        ```python
        def display_feature(
            self,
            # 参数...
        ):
            """方法文档字符串。"""
            # 本地导入以防止循环依赖
            from ..animation_functions.animate_feature import animate_feature as actual_animation_func
            
            actual_animation_func(
                scene=self,
                # 参数传递...
            )
        ```

通过遵循这些指南和最佳实践，您可以系统地将现有动画逻辑迁移到新的动画函数架构中，提高代码的模块化、可测试性和可维护性。

**阶段四：清理与文档化**

6.  **文档化：**
    *   为新的动画函数 (`animate_my_feature`)、任何辅助函数、`FeynmanScene` 调度器方法和测试函数编写清晰的文档字符串。
7.  **更新项目文档：** 如果适用，更新任何高级项目文档或示例以反映新的动画函数。

通过遵循这些步骤，您可以系统地迁移您的动画逻辑，从而提高模块化、可测试性和可维护性。记住在每个阶段都要进行彻底的测试。
