---
layout: default
title: "Home"
nav_order: 1
---

# Turns Codebase into Easy Tutorial - Pocket Flow

Ever stared at a new codebase written by others feeling completely lost? This project analyzes GitHub repositories and creates beginner-friendly tutorials explaining exactly how the code works - all powered by AI! Our intelligent system automatically breaks down complex codebases into digestible explanations that even beginners can understand.

<p align="center">
  <a href="https://github.com/The-Pocket/PocketFlow" target="_blank">
    <img 
      src="https://raw.githubusercontent.com/The-Pocket/Tutorial-Codebase-Knowledge/refs/heads/main/assets/banner.png" width="800"
    />
  </a>
</p>

This is a tutorial project of [Pocket Flow](https://github.com/The-Pocket/PocketFlow), a 100-line LLM framework. It crawls GitHub repositories and build a knowledge base from the code.

## Example Tutorials for Popular GitHub Repositories

- [AutoGen Core](./AutoGen Core/index.md) - Build AI teams that talk, think, and solve problems together like coworkers!
- [Browser Use](./Browser Use/index.md) - Let AI surf the web for you, clicking buttons and filling forms like a digital assistant!
- [Celery](./Celery/index.md) - Supercharge your app with background tasks that run while you sleep!
- [Click](./Click/index.md) - Turn Python functions into slick command-line tools with just a decorator!
- [Codex](./Codex/index.md) - Turn plain English into working code with this AI terminal wizard!
- [Crawl4AI](./Crawl4AI/index.md) - Train your AI to extract exactly what matters from any website!
- [CrewAI](./CrewAI/index.md) - Assemble a dream team of AI specialists to tackle impossible problems!
- [DSPy](./DSPy/index.md) - Build LLM apps like Lego blocks that optimize themselves!
- [FastAPI](./FastAPI/index.md) - Create APIs at lightning speed with automatic docs that clients will love!
- [Flask](./Flask/index.md) - Craft web apps with minimal code that scales from prototype to production!
- [Google A2A](./Google A2A/index.md) - The universal language that lets AI agents collaborate across borders!
- [LangGraph](./LangGraph/index.md) - Design AI agents as flowcharts where each step remembers what happened before!
- [LevelDB](./LevelDB/index.md) - Store data at warp speed with Google's engine that powers blockchains!
- [MCP Python SDK](./MCP Python SDK/index.md) - Build powerful apps that communicate through an elegant protocol without sweating the details!
- [NumPy Core](./NumPy Core/index.md) - Master the engine behind data science that makes Python as fast as C!
- [OpenManus](./OpenManus/index.md) - Build AI agents with digital brains that think, learn, and use tools just like humans do!
- [Pydantic Core](./Pydantic Core/index.md) - Validate data at rocket speed with just Python type hints!
- [Requests](./Requests/index.md) - Talk to the internet in Python with code so simple it feels like cheating!
- [SmolaAgents](./SmolaAgents/index.md) - Build tiny AI agents that punch way above their weight class!

