from manim import *
from PIL import Image as PIL_IMAGE
import requests
import numpy as np

def get_emoji(emoji):
    emoji_code = "-".join(f"{ord(c):x}" for c in emoji)
    emoji_code = emoji_code.upper() # <- this needs to be added
    url = f"https://raw.githubusercontent.com/hfg-gmuend/openmoji/master/color/618x618/{emoji_code}.png"
    im = PIL_IMAGE.open(requests.get(url, stream=True).raw)
    image = np.array(im.convert("RGBA"))
    return image

config.background_color = WHITE

class ShowEmoji(Scene):
    def construct(self):
        text = Text("狗狗", color=BLACK)
        emoji = ImageMobject(get_emoji("🐶")).set_height(text.height * 1.5)
        emoji.next_to(text, RIGHT)

        cat = Text("猫猫", color=BLACK).next_to(text, DOWN * 2)
        cat_emoji = ImageMobject(get_emoji("🐱")).set_height(cat.height * 1.5)
        cat_emoji.next_to(cat, RIGHT)
        text2 = Text("双全", color=BLACK).next_to(cat, DOWN * 2)
        
        self.play(FadeIn(text), FadeIn(cat), FadeIn(text2), FadeIn(emoji), FadeIn(cat_emoji))