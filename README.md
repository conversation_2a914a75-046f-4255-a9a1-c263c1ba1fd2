# Agentic Feynman - 智能视频生成工具

基于AI Agent的智能视频生成系统，从多种材料源自动生成高质量讲解视频。

## 🚀 快速使用

### 基本命令

```bash
python feynman_workflow_refactor.py
```
### 配置设置

在 `config/config.yaml` 中设置材料源：

```yaml
material:
  sources:
    github:
      enabled: true
      url: "https://github.com/用户名/仓库名"
    pdf:
      enabled: false
      url: "https://arxiv.org/abs/论文ID"
    chat:
      enabled: false
      purpose: "您想了解的主题"
```

## 🎬 工作流程

1. **材料解析** - 提取内容
2. **素材生成** - 生成讲解素材
3. **分镜设计** - 创建动画脚本
4. **视频渲染** - 输出最终视频

## 📁 输出文件

生成的视频保存在 `output/项目名称/videos/` 目录下。

---

# 智能网页内容分析和录制工具

这是一个基于Selenium的智能Agent，可以自动抓取网页全部元素，通过内容分析算法识别核心区域，并使用录屏技术自动对核心内容施加动态聚焦(focus)和放大(zoom in)效果。

## 功能特点

- **智能内容分析**：自动识别网页中的重要内容区域
- **动态聚焦效果**：对关键内容区域进行平滑放大和聚焦
- **自然的浏览体验**：模拟人类阅读习惯的页面浏览
- **高度可定制**：可调整视频时长、分辨率和其他参数

## 安装

1. 确保已安装 Python 3.7 或更高版本
2. 安装依赖包：

```bash
pip install -r requirements.txt
```

## 使用方法

基本用法：

```bash
python web_content_analyzer.py https://example.com
```

### 参数说明

- `url`：要分析和录制的网页URL（必需）
- `--output, -o`：输出视频文件路径（默认：output.mp4）
- `--duration, -d`：视频时长，单位秒（默认：20）
- `--width, -w`：视频宽度，单位像素（默认：1280）
- `--height, -h`：视频高度，单位像素（默认：720）
- `--fps`：视频的每秒帧数（默认：15）
- `--max-elements`：最多聚焦的关键元素数量（默认：5）
- `--headless`：使用无头模式，不显示浏览器窗口

### 示例

录制GitHub仓库页面的30秒视频：

```bash
python web_content_analyzer.py https://github.com/tensorflow/tensorflow --output tensorflow-demo.mp4 --duration 30
```

以高分辨率录制网站首页：

```bash
python web_content_analyzer.py https://www.example.com --output example-hd.mp4 --width 1920 --height 1080
```

## 工作原理

1. **页面分析**：程序首先加载目标网页，然后分析页面结构和内容
2. **内容评分**：基于位置、大小、文本相关性等因素对页面元素进行重要性评分
3. **关键内容识别**：识别得分最高的元素作为关键内容区域
4. **智能录制**：按照优化的顺序依次聚焦关键区域，并平滑过渡
5. **视频生成**：将捕获的帧合成为高质量MP4视频

## 技术细节

- 使用Selenium WebDriver进行浏览器控制
- 通过CV2(OpenCV)进行视频处理
- 使用NLTK和scikit-learn进行内容分析（如可用）
- 应用缓动函数创建平滑动画效果

## 系统要求

- Python 3.7+
- Chrome浏览器
- 足够的系统内存（推荐4GB以上）

# 如何在Storyboard框架中增加新的体裁类型

本项目采用基于体裁的内容分析和处理系统，支持针对不同内容类型（如学术论文、GitHub项目、博客文章等）进行专门优化的分镜生成。以下是添加新体裁类型的完整步骤：

## 1. 定义新体裁类型

在 `agents/generate_manim_dsl_agent_refactored.py` 文件中的 `ContentGenre` 枚举类中添加新的体裁类型：

```python
class ContentGenre(Enum):
    """内容体裁类型"""
    PAPER = "paper"           # 学术论文（通用）
    REVIEW_PAPER = "review_paper"  # 综述论文
    TECH_REPORT = "tech_report"    # 技术报告论文
    GITHUB = "github"         # GitHub项目
    BLOG = "blog"             # 网页博客
    CASE_STUDY = "case_study" # 综合案例
    BOOK = "book"             # 本地书籍
    GENERAL = "general"       # 通用内容
    # 添加新的体裁类型
    TUTORIAL = "tutorial"     # 教程类内容
    NEWS = "news"             # 新闻资讯
    DOCUMENTATION = "documentation"  # 技术文档
```

## 2. 更新体裁检测逻辑

在 `GenreDetector.detect_genre()` 方法中添加新体裁的关键词检测逻辑：

```python
@staticmethod
def detect_genre(content: str, purpose: str = "") -> ContentGenre:
    """根据用户目的检测体裁类型"""
    purpose_lower = purpose.lower()
    logger.info(f"开始体裁检测 - 用户目的: '{purpose}'")
    
    # 现有检测逻辑...
    
    # 添加新体裁的检测逻辑
    elif any(keyword in purpose_lower for keyword in ['教程', 'tutorial', '指南', '入门', '学习']):
        logger.info("✅ 检测到关键词匹配 -> 教程体裁")
        return ContentGenre.TUTORIAL
    elif any(keyword in purpose_lower for keyword in ['新闻', 'news', '资讯', '报道', '动态']):
        logger.info("✅ 检测到关键词匹配 -> 新闻体裁")
        return ContentGenre.NEWS
    elif any(keyword in purpose_lower for keyword in ['文档', 'documentation', 'docs', 'api', '手册']):
        logger.info("✅ 检测到关键词匹配 -> 技术文档体裁")
        return ContentGenre.DOCUMENTATION
    
    # 默认为通用类型
    logger.info("⚠️ 未检测到特定关键词，使用通用体裁")
    return ContentGenre.GENERAL
```

## 3. 添加体裁模板文件映射

在 `PromptLoader._load_templates()` 方法中的 `genre_files` 字典中添加新体裁的模板文件映射：

```python
# 加载各体裁专用模板
genre_files = {
    ContentGenre.PAPER: "paper_prompt.md",
    ContentGenre.REVIEW_PAPER: "review_paper_prompt.md",
    ContentGenre.TECH_REPORT: "tech_report_prompt.md",
    ContentGenre.GITHUB: "github_prompt.md", 
    ContentGenre.BLOG: "blog_prompt.md",
    ContentGenre.CASE_STUDY: "case_study_prompt.md",
    ContentGenre.BOOK: "book_prompt.md",
    # 添加新体裁的模板文件映射
    ContentGenre.TUTORIAL: "tutorial_prompt.md",
    ContentGenre.NEWS: "news_prompt.md",
    ContentGenre.DOCUMENTATION: "documentation_prompt.md"
}
```

## 4. 创建体裁专用Prompt模板

在 `prompts/storyboard_prompts/` 目录下创建新的体裁专用prompt模板文件。以教程体裁为例，创建 `tutorial_prompt.md`：

```markdown
# 教程体裁专用Prompt

## 教程内容特色要求

### 内容理解重点
- **学习目标**: 明确教程要教会用户什么技能或知识
- **难度层次**: 识别内容的难度级别，适配目标受众
- **实践性**: 突出动手操作和实际应用的部分

### 分镜设计原则
- **循序渐进**: 按照学习的逻辑顺序组织内容
- **重点突出**: 对关键概念和操作步骤进行重点展示
- **实例驱动**: 优先使用具体的代码示例和操作演示

### 特殊处理要点
- **必须** 在每个关键步骤前添加"第X步"或"接下来"等引导词
- **必须** 对代码块和操作截图给予特别关注
- **必须** 使用渐进式的信息展示，避免一次性展示过多内容
- **必须** 在适当位置添加学习提示和注意事项
```

## 5. 体裁模板设计最佳实践

设计新体裁模板时，请遵循以下原则：

#### 模板结构
- **内容理解重点**: 定义该体裁内容的核心特征
- **分镜设计原则**: 指导如何组织和展示该体裁的内容
- **特殊处理要点**: 该体裁特有的处理要求和注意事项

#### 内容要素
- **必须项**: 使用"必须"标记不可忽略的要求
- **优先项**: 使用"优先"标记重要但可选的建议
- **禁止项**: 明确该体裁应避免的处理方式

#### 示例参考
可以参考现有的体裁模板文件：
- `github_prompt.md`: 项目展示类内容
- `paper_prompt.md`: 学术严谨类内容  
- `blog_prompt.md`: 轻松易读类内容
- `tutorial_prompt.md`: 教学指导类内容

