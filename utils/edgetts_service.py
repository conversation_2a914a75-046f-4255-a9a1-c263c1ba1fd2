from pathlib import Path

import edge_tts
from manim_voiceover.services.base import SpeechService


class EdgeTTSService(SpeechService):
    def __init__(self, voice, global_speed: float = 1.00, cache_dir: str = None, **kwargs):
        super().__init__(global_speed, cache_dir, **kwargs)
        self.voice = voice

    def generate_from_text(self, text, cache_dir=None, path=None):
        if cache_dir is None:
            cache_dir = self.cache_dir
        input_data = {"input_text": text, "service": "edgetts"}
        cached_result = self.get_cached_result(input_data, cache_dir)
        if cached_result is not None:
            return cached_result

        if path is None:
            audio_path = self.get_audio_basename(input_data) + ".mp3"
        else:
            audio_path = path

        communicate = edge_tts.Communicate(text, self.voice, proxy="http://127.0.0.1:7890")
        communicate.save_sync(str(Path(cache_dir) / audio_path))

        json_dict = {
            "input_text": text,
            "input_data": input_data,
            "original_audio": audio_path,
        }

        return json_dict
