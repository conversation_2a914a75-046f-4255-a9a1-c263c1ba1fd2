from typing import Any, ForwardRef, Optional, Union

from pydantic import BaseModel

# Define Pydantic models for the output formats
# Use ForwardRef for self-referencing models
OutlineSubSectionRef = ForwardRef("OutlineSubSection")


class OutlineSubSection(BaseModel):
    标题: str
    内容: str
    子章节: list[OutlineSubSectionRef] = []


# Update the forward reference
OutlineSubSection.update_forward_refs()


class OutlineSection(BaseModel):
    标题: str
    内容: str
    子章节: list[OutlineSubSection] = []


class OutlineResponseFormat(BaseModel):
    outline: list[OutlineSection]

    @classmethod
    def model_validate(cls, obj):
        """Custom validation to handle both direct list and dictionary with outline field."""
        if isinstance(obj, list):
            # If the input is a list, wrap it in a dictionary
            return cls(outline=obj)
        return super().model_validate(obj)


class StoryboardItem(BaseModel):
    分镜名: str
    内容要点: str
    讲解文案: str
    素材名: str
    视觉动效建议: str


class StoryboardResponseFormat(BaseModel):
    storyboard: list[StoryboardItem]

    @classmethod
    def model_validate(cls, obj):
        """Custom validation to handle both direct list and dictionary with storyboard field."""
        if isinstance(obj, list):
            # If the input is a list, wrap it in a dictionary
            return cls(storyboard=obj)
        return super().model_validate(obj)


class MediaObject(BaseModel):
    medias: str
    subtitle: str


class ContentStep(BaseModel):
    ttscontext: str
    context: str = "为空"
    keywords: list[str]
    before: Optional[MediaObject] = None
    after: Optional[dict[str, str]] = None
    object: Optional[dict[str, str]] = None
    action: str = "Display"


class ContentExplanation(BaseModel):
    steps: list[ContentStep]


class ContentIntroduction(BaseModel):
    ttscontext: str
    context: str = "为空"
    keywords: list[str]


class ContentResponseFormat(BaseModel):
    introduction: ContentIntroduction
    explanation: ContentExplanation

    @classmethod
    def model_validate(cls, obj):
        """Custom validation to handle different input formats."""
        if isinstance(obj, dict) and "steps" in obj and "introduction" not in obj:
            # If the input has 'steps' directly but no 'introduction', restructure it
            steps = obj.pop("steps", [])
            introduction = obj.pop("introduction", {"ttscontext": "", "context": "为空", "keywords": []})
            return cls(introduction=introduction, explanation={"steps": steps})
        return super().model_validate(obj)


# Helper functions for model validation and JSON handling
import json
import logging
import re

logger = logging.getLogger(__name__)


def extract_json(text: str) -> Optional[dict[str, Any]]:
    """
    从文本中提取JSON数据

    Args:
        text: 包含JSON的文本

    Returns:
        Optional[Dict]: 解析后的JSON对象，失败时返回None
    """
    # 检查输入类型
    if isinstance(text, (dict, list)):
        return text

    # 如果输入不是字符串，转换为字符串
    if not isinstance(text, str):
        text = str(text)

    # 尝试直接解析整个文本
    try:
        return json.loads(text)
    except json.JSONDecodeError:
        pass

    # 尝试提取```json代码块
    json_pattern = r"```(?:json)?\s*([\s\S]*?)```"
    matches = re.findall(json_pattern, text)

    for match in matches:
        try:
            # 清理内容
            cleaned_match = match.strip()
            return json.loads(cleaned_match)
        except json.JSONDecodeError:
            continue

    # 尝试提取JSON数组 [...]
    array_pattern = r"\[\s*\{[\s\S]*?\}\s*\]"
    array_matches = re.findall(array_pattern, text)

    for match in array_matches:
        try:
            # 清理内容
            cleaned_match = match.strip()
            return json.loads(cleaned_match)
        except json.JSONDecodeError:
            continue

    # 尝试提取单个JSON对象 {...}
    object_pattern = r"\{[\s\S]*?\}"
    object_matches = re.findall(object_pattern, text)

    for match in object_matches:
        try:
            # 清理内容
            cleaned_match = match.strip()
            return json.loads(cleaned_match)
        except json.JSONDecodeError:
            continue

    # 最后尝试一种更宽松的解析方式：查找[开始直到]结束
    if "[" in text and "]" in text:
        start_idx = text.find("[")
        end_idx = text.rfind("]") + 1
        if start_idx < end_idx:
            try:
                json_segment = text[start_idx:end_idx]
                return json.loads(json_segment)
            except json.JSONDecodeError:
                pass

    # 所有尝试都失败，创建基本结构
    logger.warning("无法从文本中提取有效的JSON，创建默认结构")
    if "section_name" in text:
        return [
            {
                "section_name": "项目概述",
                "key_points": "仓库分析",
                "section_content": "无法解析仓库内容，请检查仓库URL是否正确",
                "code_snippet": "",
                "visualization_suggestion": "",
            }
        ]

    # 返回空数组，避免KeyError
    return []


def extract_xml(text: str, tag_name: str) -> Optional[str]:
    """
    从文本中提取XML标签内容

    Args:
        text: 包含XML标签的文本
        tag_name: 标签名称

    Returns:
        Optional[str]: 标签内容，未找到时返回None
    """
    # 检查输入是否为字典类型，如果是则返回None
    if isinstance(text, dict):
        logger.warning(f"extract_xml收到了字典类型而非字符串: {text}")
        return None

    pattern = rf"<{tag_name}>([\s\S]*?)</{tag_name}>"
    match = re.search(pattern, text)

    if match:
        return match.group(0)
    return None


def extract_code(text: str) -> Optional[str]:
    """
    从文本中提取代码块

    Args:
        text: 包含代码块的文本

    Returns:
        Optional[str]: 提取的代码，未找到时返回None
    """
    # 尝试提取```python```代码块
    code_pattern = r"```(?:python)?\s*([\s\S]*?)```"
    matches = re.findall(code_pattern, text)

    if matches:
        return matches[0]

    # 尝试提取<CODE>标签中的内容
    code_tag_pattern = r"<CODE>([\s\S]*?)</CODE>"
    match = re.search(code_tag_pattern, text)

    if match:
        # 移除可能的```python```标记
        code = match.group(1)
        return re.sub(r"```(?:python)?\s*([\s\S]*?)```", r"\1", code)

    return None


def save_json_content(content: Union[dict, list], file_path: str) -> bool:
    """
    保存JSON内容到文件

    Args:
        content: JSON内容
        file_path: 文件路径

    Returns:
        bool: 成功返回True，失败返回False
    """
    try:
        with open(file_path, "w", encoding="utf-8") as f:
            json.dump(content, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        logger.error(f"保存JSON内容失败: {str(e)}")
        return False


def get_format_description(format_class):
    """Get description of format class for retry prompts"""
    if hasattr(format_class, "__annotations__"):
        fields = []
        for field_name, field_type in format_class.__annotations__.items():
            field_desc = f"{field_name}: {field_type}"
            fields.append(field_desc)

        return f"类 {format_class.__name__} 的字段:\n" + "\n".join(fields)
    return f"类 {format_class.__name__} 的结构未知"


def format_prompt_for_json(prompt, format_class):
    """Format a prompt to request JSON output in the specified format"""
    format_desc = get_format_description(format_class)

    formatted_prompt = (
        f"{prompt}\n\n"
        f"请按照以下JSON格式返回结果：\n"
        f"{format_desc}\n\n"
        f"请确保返回的是有效的JSON格式，不要包含任何其他解释或说明。"
    )

    return formatted_prompt


def wrap_text(text: str, max_length: int = 70) -> list[str]:
    """
    将文本按指定长度拆分成多行，确保：
    1. 不会在单词中间拆分
    2. 不会在标点符号前拆分
    3. 考虑中英文混合的显示宽度

    Args:
        text: 需要拆分的文本
        max_length: 每行最大长度

    Returns:
        list[str]: 拆分后的文本行列表
    """
    import re
    from math import ceil

    import wcwidth

    # 清理多余空格
    text = " ".join(text.split())
    total_width = wcwidth.wcswidth(text)
    n_chunks = ceil(total_width / max_length)
    target_width = total_width / n_chunks

    # 正则将文本拆分为：CJK单字符、英文单词、标点符号和空白
    token_pattern = r"[\u4e00-\u9fff]|[A-Za-z0-9]+|[^\w\s\u4e00-\u9fff]+|\s+"
    tokens = re.findall(token_pattern, text)

    chunks = []
    current_chunk = ""
    current_width = 0

    for token in tokens:
        token_width = wcwidth.wcswidth(token)

        # 当累计宽度超出目标宽度，并且当前已有内容时分割
        if current_chunk and (current_width + token_width > target_width):
            # 避免在标点前拆分：若 token 非空且首字符非字母数字（例如标点）
            if token.strip() and not token[0].isalnum():
                current_chunk += token
                chunks.append(current_chunk)
                current_chunk = ""
                current_width = 0
            else:
                chunks.append(current_chunk)
                current_chunk = token
                current_width = token_width
        else:
            current_chunk += token
            current_width += token_width

    if current_chunk:
        chunks.append(current_chunk)

    # 如果最后一段宽度太小，则与前一段合并
    if len(chunks) > 1 and wcwidth.wcswidth(chunks[-1]) < (target_width / 4):
        chunks[-2] += chunks[-1]
        chunks.pop()

    return chunks


if __name__ == "__main__":
    text = """唐玄宗执政前期，唐朝达到鼎盛，万国来朝。唐玄宗李隆基即位初期，任用贤臣，革除弊政，发展生产，使唐朝进入了前所未有的全盛时期，史称“开元盛世”。此时唐朝政治清明，经济文化空前繁荣，长安成为举世闻名的国际大都市。"""
    chunks = wrap_text(text, max_length=20)
    print("\n".join(chunks))
