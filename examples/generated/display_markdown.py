# Generated by <PERSON>ynman DSL v2 Code Generator
# Author: 用户
# -*- coding: utf-8 -*-

import sys
import os
root_dir = os.getcwd()
sys.path.append(root_dir)

from manim import *
from dsl.v2.core.scene import FeynmanScene
from dsl.v2.animation_functions import *  # IMPORTANT: Ensures all animations are registered

class aa(FeynmanScene):
    config.background_color = '#FFFFFF'

    def construct(self):
        self.add_background()

        # Action 1: animate_markdown
        animate_markdown(
            scene=self,
            content="## 自定义验证与序列化逻辑\n\n1. 超越**基本类型**检查\n2. 实现**复杂规则**或*数据转换*。",
            id="custom_logic_title",
            narration="如果需要更复杂的验证规则或在序列化时转换数据格式，怎么办？"
        )
        # --- Final wait to hold the last frame ---
        self.wait(1)
