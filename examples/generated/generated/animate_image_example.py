# Generated by Feynman DSL v2 Code Generator
# Author: 自动生成
# -*- coding: utf-8 -*-

import sys
import os
root_dir = os.getcwd()
sys.path.append(root_dir)

from manim import *
from dsl.v2.core.scene import FeynmanScene
from dsl.v2.animation_functions import *  # IMPORTANT: Ensures all animations are registered

class animate_image_(FeynmanScene):
    config.background_color = '#FFFFFF'

    def construct(self):
        self.add_background()

        # Action 1: animate_image
        animate_image(
            scene=self,
            image_path="assets/manim_logo.png",
            narration="让我们看看这张图片。",
            annotation="这是一张示例图片，展示了重要的内容。"
        )

        # Action 2: animate_image
        animate_image(
            scene=self,
            image_path="assets/manim_logo.png",
            id="architecture_diagram",
            annotation="## 系统架构\n- 前端组件\n- 后端服务\n- 数据存储\n",
            narration="这张架构图展示了系统的主要组件和它们之间的关系。"
        )
        # --- Final wait to hold the last frame ---
        self.wait(1)
