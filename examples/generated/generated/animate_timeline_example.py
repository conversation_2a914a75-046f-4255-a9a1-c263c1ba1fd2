# Generated by <PERSON><PERSON>man DSL v2 Code Generator
# Author: 自动生成
# -*- coding: utf-8 -*-

import sys
import os
root_dir = os.getcwd()
sys.path.append(root_dir)

from manim import *
from dsl.v2.core.scene import FeynmanScene
from dsl.v2.animation_functions import *  # IMPORTANT: Ensures all animations are registered

class animate_timeline_(FeynmanScene):
    config.background_color = '#FFFFFF'

    def construct(self):
        self.add_background()

        # Action 1: animate_timeline
        animate_timeline(
            scene=self,
            events=[{'year': '1950', 'title': 'The Beginning', 'description': 'A new era starts.', 'emoji': '🌍', 'color': '#F0E68C'}, {'year': '1965', 'title': 'Major Discovery', 'description': 'Key findings published.', 'emoji': '🌞', 'color': '#FFA500'}, {'year': '1980', 'title': 'Expansion', 'description': 'Growth and development.', 'emoji': '🌛', 'color': '#B22222'}, {'year': '2000', 'title': 'New Century', 'description': 'Looking ahead.', 'emoji': '🪵', 'color': '#FFC0CB'}, {'year': '2020', 'title': 'Modern Times', 'description': 'Current state of affairs.', 'emoji': '🚀', 'color': '#9370DB'}],
            title="Historical Timeline",
            narration="A journey through time, highlighting key moments."
        )
        # --- Final wait to hold the last frame ---
        self.wait(1)
