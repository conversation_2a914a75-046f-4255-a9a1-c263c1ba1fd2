# Generated by <PERSON>ynman DSL v2 Code Generator
# Author: 自动生成
# -*- coding: utf-8 -*-

import sys
import os
root_dir = os.getcwd()
sys.path.append(root_dir)

from manim import *
from dsl.v2.core.scene import FeynmanScene
from dsl.v2.animation_functions import *  # IMPORTANT: Ensures all animations are registered

class animate_counter_(FeynmanScene):
    config.background_color = '#000000'

    def construct(self):
        self.add_background()

        # Action 1: animate_counter
        animate_counter(
            scene=self,
            counter_type="counter",
            target_value=100,
            label="进度",
            unit="%",
            duration=3,
            effect="flash",
            narration="加载进度达到100%。"
        )

        # Action 2: animate_counter
        animate_counter(
            scene=self,
            counter_type="curve",
            target_value=500,
            label="用户增长曲线",
            unit="用户",
            duration=4,
            narration="用户数量快速增长至500。"
        )
        # --- Final wait to hold the last frame ---
        self.wait(1)
