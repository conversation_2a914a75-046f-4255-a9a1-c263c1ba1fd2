{"schema_version": "2.0-mvp", "metadata": {"title": "animate_chart 示例", "author": "自动生成", "background_color": "BLACK"}, "actions": [{"type": "animate_chart", "params": {"chart_type": "bar", "data": {"苹果": 75, "香蕉": 120, "橙子": 90}, "title": "水果销量", "narration": "这是本月水果销量的条形图。", "x_label": "水果", "y_label": "销量 (千克)"}}, {"type": "animate_chart", "params": {"chart_type": "line", "data": [{"第一季度": 50, "第二季度": 65, "第三季度": 80, "第四季度": 70}, {"第一季度": 40, "第二季度": 50, "第三季度": 60, "第四季度": 90}], "title": "产品A vs 产品B 季度销售额", "dataset_names": ["产品A", "产品B"], "narration": "此折线图比较了产品A和产品B的季度销售额。", "x_label": "季度", "y_label": "销售额 (万元)"}}, {"type": "animate_chart", "params": {"chart_type": "radar", "data": [{"性能": 8, "价格": 6, "外观": 9, "易用性": 7, "可靠性": 8}, {"性能": 9, "价格": 4, "外观": 7, "易用性": 8, "可靠性": 9}], "title": "产品对比", "dataset_names": ["产品X", "产品Y"], "narration": "这个雷达图展示了两款产品在五个维度上的评分对比。"}}]}