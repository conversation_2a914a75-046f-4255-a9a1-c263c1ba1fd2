{"schema_version": "2.0-mvp", "metadata": {"title": "animate_side_by_side_comparison 示例", "author": "自动生成", "background_color": "BLACK"}, "actions": [{"type": "side_by_side_comparison", "params": {"left_content": "def fib_recursive(n):\n    if n <= 1:\n        return n\n    return fib_recursive(n-1) + fib_recursive(n-2)\n", "left_type": "code", "left_title": "递归斐波那契", "right_content": "def fib_iterative(n):\n    a, b = 0, 1\n    for _ in range(n):\n        a, b = b, a + b\n    return a\n", "right_type": "code", "right_title": "迭代斐波那契", "narration": "让我们比较斐波那契数列的递归和迭代实现。"}}, {"type": "side_by_side_comparison", "params": {"left_content": "{\n    \"name\": \"<PERSON>\",\n    \"year\": 1991,\n    \"creator\": \"<PERSON>\",\n    \"paradigms\": [\"面向对象\", \"命令式\", \"函数式\"]\n}\n", "left_type": "json", "left_title": "Python 信息", "right_content": "{\n    \"name\": \"Java\",\n    \"year\": 1995,\n    \"creator\": \"<PERSON>\",\n    \"paradigms\": [\"面向对象\", \"命令式\"]\n}\n", "right_type": "json", "right_title": "Java 信息", "transition": "fadeIn", "narration": "Python 和 Java 的信息对比。"}}, {"type": "side_by_side_comparison", "params": {"left_content": "# Python\n- 创建于1991年\n- 由<PERSON> van <PERSON>开发\n- 支持面向对象、命令式和函数式编程\n", "left_type": "markdown", "left_title": "Python 信息", "right_content": "# Java\n- 创建于1995年\n- 由<PERSON>开发\n- 主要支持面向对象编程\n", "right_type": "markdown", "right_title": "Java 信息", "transition": "fadeIn", "narration": "Python 和 Java 的信息对比。"}}]}