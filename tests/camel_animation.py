from manim import *
import numpy as np

class CamelStockComparison(Scene):
    def construct(self):
        # Create background elements
        gradient, network, center_mask = self.create_hyperbolic_network_background()
        
        # Add the background to the scene
        self.add(gradient, center_mask)
        
        # Add the network and start rotation animation in the background
        self.add(network)
        
        # Create rotation updater function
        def rotate_network(mob, dt):
            mob.rotate(dt * 0.1)  # Rotate at a speed of 0.1 radians per dt
        
        # Add the continuous rotation updater
        network.add_updater(rotate_network)
        self.add(network)
        
        # Now present the slides while the background rotates
        # Slide 1: Title with artistic typography
        self.create_title_slide()
        
        # Slide 2: AI Agent Introduction
        self.create_intro_slide()
        
        # Slide 3: Stock Comparison Data
        self.create_comparison_slide()
        
        # Slide 4: Conclusion
        self.create_conclusion_slide()
        
        # Remove the updater at the end
        network.remove_updater(rotate_network)
    
    def create_hyperbolic_network_background(self):
        # Create a gradient background from orange to white
        orange = "#FF8C00"
        light_orange = "#FFE0B2"
        
        # Main background - using config.frame_width and config.frame_height
        gradient = Rectangle(
            width=config.frame_width,
            height=config.frame_height,
            fill_opacity=1,
            stroke_width=0
        ).set_color_by_gradient([light_orange, WHITE, light_orange])
        
        # Create hyperbolic network with regular pattern
        network = VGroup()
        
        # Parameters for the hyperbolic grid
        num_radial_lines = 16
        num_circles = 8
        max_radius = 10
        
        # Create radial lines
        for i in range(num_radial_lines):
            angle = i * TAU / num_radial_lines
            line = Line(
                ORIGIN,
                max_radius * np.array([np.cos(angle), np.sin(angle), 0]),
                stroke_width=1.2,
                stroke_opacity=0.3,
                stroke_color=orange
            )
            network.add(line)
        
        # Create concentric circles
        for i in range(1, num_circles):
            radius = i * max_radius / num_circles
            circle = Circle(
                radius=radius,
                stroke_width=1.2,
                stroke_opacity=0.3,
                stroke_color=orange
            )
            network.add(circle)
        
        # Create hyperbolic curves connecting points
        for i in range(num_radial_lines):
            for j in range(i + 2, num_radial_lines, 3):  # Connect to every third line
                angle1 = i * TAU / num_radial_lines
                angle2 = j * TAU / num_radial_lines
                
                # Get points on different circles for a more interesting pattern
                radius1 = (i % 3 + 2) * max_radius / num_circles
                radius2 = (j % 3 + 2) * max_radius / num_circles
                
                start = radius1 * np.array([np.cos(angle1), np.sin(angle1), 0])
                end = radius2 * np.array([np.cos(angle2), np.sin(angle2), 0])
                
                # Create a curved path between points
                control = np.array([
                    (start[0] + end[0]) * 0.5,
                    (start[1] + end[1]) * 0.5,
                    0
                ]) * 0.5  # Pull control point toward center for hyperbolic effect
                
                curve = CubicBezier(
                    start,
                    start * 0.6 + control * 0.4,
                    end * 0.6 + control * 0.4,
                    end,
                    stroke_width=0.8,
                    stroke_opacity=0.2,
                    stroke_color=orange
                )
                network.add(curve)
        
        # Scale the network to fit the screen
        network.scale(0.9)
        
        # Add a central node
        central_node = Circle(
            radius=0.15,
            fill_opacity=0.5,
            stroke_width=1.5,
            stroke_color=orange,
            fill_color=orange
        )
        
        # Add some smaller nodes at intersection points
        nodes = VGroup()
        for i in range(1, num_circles, 2):
            for j in range(0, num_radial_lines, 4):
                angle = j * TAU / num_radial_lines
                radius = i * max_radius / num_circles
                position = radius * np.array([np.cos(angle), np.sin(angle), 0])
                
                node = Circle(
                    radius=0.08,
                    fill_opacity=0.4,
                    stroke_width=1,
                    stroke_color=orange,
                    fill_color=orange
                ).move_to(position)
                nodes.add(node)
        
        network.add(central_node, nodes)
        
        # Create a clear space in the center for content
        # Use a solid white background for center to ensure text is clear
        center_mask = Circle(
            radius=5.5,
            fill_opacity=1.0,  # Fully opaque
            stroke_width=0,
            fill_color=WHITE
        )
        
        return gradient, network, center_mask
    
    def create_title_slide(self):
        # Define colors
        orange = "#FF8C00"
        dark_orange = "#E65100"
        navy_blue = "#0D47A1"  # Dark blue for better contrast
        
        # Create content area with proper spacing
        content_area = Rectangle(
            width=10,
            height=5,
            fill_opacity=0,
            stroke_opacity=0
        )
        
        # Create artistic title with shadow effect
        title = Text("AI帮你选股？", font_size=64, color=dark_orange)
        title.set_stroke(width=1.5, color=navy_blue, opacity=0.8)
        title.set_fill(color=dark_orange)
        
        subtitle = Text("实测 CAMEL Agent 对比 AAPL vs NVDA", font_size=36, color=navy_blue)
        
        # Add decorative elements - position more towards center
        decoration = VGroup()
        # Left decoration
        left_dec = Line([-3, 0, 0], [-1, 0, 0], stroke_width=3, color=orange)
        # Right decoration
        right_dec = Line([1, 0, 0], [3, 0, 0], stroke_width=3, color=orange)
        # Circle in the middle
        circle = Circle(radius=0.2, stroke_width=3, color=orange).move_to([0, 0, 0])
        
        decoration.add(left_dec, right_dec, circle)
        
        # Arrange all elements with proper spacing
        content_group = VGroup(title, subtitle, decoration)
        content_group.arrange(DOWN, buff=0.6)  # More space between elements
        content_group.move_to(content_area.get_center())
        
        # Title slide animation
        self.play(
            Write(title, run_time=1.5),
            FadeIn(decoration, run_time=1)
        )
        self.play(
            Write(subtitle, run_time=1.5)
        )
        self.wait(1)
        self.play(
            FadeOut(title),
            FadeOut(subtitle),
            FadeOut(decoration),
            run_time=1
        )
    
    def create_intro_slide(self):
        # Define colors
        orange = "#FF8C00"
        dark_orange = "#E65100"
        dark_teal = "#00695C"  # Dark teal for better contrast
        
        # Create a safe content area with proper padding
        content_area = Rectangle(
            width=10,
            height=6.5,
            fill_opacity=0,
            stroke_opacity=0
        )
        
        # Create slide title with artistic effect
        slide2_title = Text("AI Agent 股票分析：从零到有", font_size=46, color=dark_orange)
        slide2_title.set_stroke(width=1, color=dark_teal, opacity=0.7)
        
        # Create content with more appealing bullets
        bullet_points = [
            "CAMEL: 专业股票分析师AI框架",
            "自动获取股票行情与财务指标",
            "对比评估苹果(AAPL)与英伟达(NVDA)"
        ]
        
        bullets = VGroup()
        for i, point in enumerate(bullet_points):
            # Create bullet point with icon
            icon = Circle(radius=0.1, fill_opacity=1, color=orange)
            text = Text(point, font_size=32, color=dark_teal)  # Dark teal for better contrast
            
            bullet = VGroup(icon, text)
            bullet.arrange(RIGHT, buff=0.3, aligned_edge=LEFT)
            bullets.add(bullet)
        
        # Arrange bullets with proper spacing
        bullets.arrange(DOWN, buff=0.7)
        
        # Add decorative element
        separator = Line(
            start=[-4, 0, 0],
            end=[4, 0, 0],
            stroke_width=2,
            stroke_opacity=0.7,
            color=orange
        )
        
        # Arrange all content with proper spacing to avoid overlap
        content_group = VGroup()
        content_group.add(slide2_title)
        content_group.add(separator)
        content_group.add(bullets)
        
        # Arrange vertically with sufficient space between elements
        content_group.arrange(DOWN, buff=0.6, aligned_edge=ORIGIN)
        content_group.move_to(content_area.get_center())
        
        # Animation
        self.play(
            Write(slide2_title),
            Create(separator),
            run_time=1.2
        )
        
        for bullet in bullets:
            self.play(
                FadeIn(bullet[0], shift=RIGHT*0.3),
                Write(bullet[1]),
                run_time=0.8
            )
        
        self.wait(1.5)
        self.play(
            FadeOut(slide2_title),
            FadeOut(separator),
            FadeOut(bullets),
            run_time=1
        )
    
    def create_comparison_slide(self):
        # Define colors
        orange = "#FF8C00"
        dark_orange = "#E65100"
        dark_purple = "#4A148C"  # Dark purple for better contrast
        green = "#2E7D32"        # Darker green for better contrast
        
        # Create a safe content area
        content_area = Rectangle(
            width=10,
            height=7,
            fill_opacity=0,
            stroke_opacity=0
        )
        
        # Create slide title with artistic effect
        slide3_title = Text("核心数据对比", font_size=46, color=dark_orange)
        slide3_title.set_stroke(width=1, color=dark_purple, opacity=0.7)
        
        # Separator
        separator = Line(
            start=[-4, 0, 0],
            end=[4, 0, 0],
            stroke_width=2,
            stroke_opacity=0.7,
            color=orange
        )
        
        table_title = Text("苹果 vs 英伟达 关键指标", font_size=34, color=dark_purple)
        
        # Create more elegant table with reduced size
        table_data = [
            ["指标", "苹果 (AAPL)", "英伟达 (NVDA)"],
            ["最新股价", "$210.20", "$134.98"],
            ["市值", "3.50万亿", "2.91万亿"],
            ["市盈率", "37.29", "39.90"],
            ["净利润/每股", "6.11", "2.97"],
            ["流动比率", "0.87", "4.44"]
        ]
        
        # Create table with rounded rectangle cells - smaller size
        table = VGroup()
        cell_width = 2.6  # Even smaller width
        cell_height = 0.5  # Even smaller height
        
        for i, row in enumerate(table_data):
            row_mob = VGroup()
            for j, item in enumerate(row):
                # Create cell background
                if i == 0:  # Header row
                    rect = RoundedRectangle(
                        width=cell_width,
                        height=cell_height,
                        corner_radius=0.1,
                        fill_opacity=0.2,
                        stroke_width=1,
                        stroke_color=orange,
                        fill_color=orange
                    )
                    text_color = "#FFFFFF"  # White color for header text on orange background
                else:
                    rect = RoundedRectangle(
                        width=cell_width,
                        height=cell_height,
                        corner_radius=0.1,
                        fill_opacity=0.05,
                        stroke_width=1,
                        stroke_color=orange,
                        fill_color="#FFFFFF"
                    )
                    text_color = dark_purple  # Dark purple for data text
                
                # Create text - smaller font
                cell_text = Text(item, font_size=22, color=text_color)
                cell_text.move_to(rect.get_center())
                
                # Combine background and text
                cell = VGroup(rect, cell_text)
                cell.move_to([
                    (j-1) * (cell_width + 0.15),  # Less horizontal space
                    -i * (cell_height + 0.12),    # Less vertical space
                    0
                ])
                row_mob.add(cell)
            table.add(row_mob)
        
        # Arrange all content with proper spacing
        header_group = VGroup(slide3_title, separator)
        header_group.arrange(DOWN, buff=0.3)
        
        content_group = VGroup(header_group, table_title, table)
        content_group.arrange(DOWN, buff=0.5)
        content_group.move_to(content_area.get_center())
        
        # Animation
        self.play(
            Write(slide3_title),
            Create(separator),
            run_time=1
        )
        self.play(Write(table_title), run_time=1)
        
        # Show table header with slide animation
        self.play(FadeIn(table[0], shift=DOWN*0.3), run_time=0.8)
        
        # Show data rows with cascade effect
        for i in range(1, len(table)):
            self.play(FadeIn(table[i], shift=DOWN*0.3), run_time=0.4)
        
        # Highlight better values with animated color change
        apple_better = [(1,1), (2,1), (4,1)]  # row, column for Apple's better metrics
        nvidia_better = [(5,2)]  # row, column for Nvidia's better metrics
        
        for row, col in apple_better:
            self.play(
                table[row][col][1].animate.set_color(green),
                table[row][col][0].animate.set_stroke(color=green),
                run_time=0.3
            )
        
        for row, col in nvidia_better:
            self.play(
                table[row][col][1].animate.set_color(green),
                table[row][col][0].animate.set_stroke(color=green),
                run_time=0.3
            )
        
        self.wait(1.5)
        self.play(
            FadeOut(slide3_title),
            FadeOut(separator),
            FadeOut(table_title),
            FadeOut(table),
            run_time=1
        )
    
    def create_conclusion_slide(self):
        # Define colors
        orange = "#FF8C00"
        dark_orange = "#E65100"
        dark_blue = "#0D47A1"  # Dark blue for better contrast
        
        # Create a safe content area
        content_area = Rectangle(
            width=10,
            height=7,
            fill_opacity=0,
            stroke_opacity=0
        )
        
        # Create slide title with artistic effect
        slide4_title = Text("AI Agent 的价值与未来", font_size=46, color=dark_orange)
        slide4_title.set_stroke(width=1, color=dark_blue, opacity=0.7)
        
        # Separator
        separator = Line(
            start=[-4, 0, 0],
            end=[4, 0, 0],
            stroke_width=2,
            stroke_opacity=0.7,
            color=orange
        )
        
        # Value points with icons
        value_points = [
            "自动化数据收集与整理",
            "快速生成对比分析",
            "提供基于数据的初步洞察"
        ]
        
        icons = [
            "⚙️", # Gear for automation
            "⚡", # Lightning for speed
            "💡"  # Lightbulb for insights
        ]
        
        values = VGroup()
        for i, (point, icon) in enumerate(zip(value_points, icons)):
            # Create icon
            icon_text = Text(icon, font_size=32)
            
            # Create text with better contrast
            text = Text(point, font_size=30, color=dark_blue)
            
            # Background for the point - adjusted size
            bg = RoundedRectangle(
                width=text.width + icon_text.width + 1.0,  # Narrower
                height=text.height + 0.3,  # Shorter
                corner_radius=0.2,
                fill_opacity=0.1,
                stroke_width=1,
                stroke_opacity=0.7,
                stroke_color=orange,
                fill_color=orange
            )
            
            # Group elements
            point_group = VGroup(bg, icon_text, text)
            text.next_to(icon_text, RIGHT, buff=0.3)
            VGroup(icon_text, text).move_to(bg.get_center())
            
            values.add(point_group)
        
        # Ensure values are properly spaced
        values.arrange(DOWN, buff=0.4)
        
        # Final message with fancy style
        final_msg = Text("AI赋能金融投资决策", font_size=38, color=dark_orange)
        final_msg.set_stroke(width=1, color=dark_blue, opacity=0.8)
        
        # Decorative underline
        underline = Line(
            start=[-2.8, 0, 0],
            end=[2.8, 0, 0],
            stroke_width=3,
            color=orange
        )
        
        final_group = VGroup(final_msg, underline)
        underline.next_to(final_msg, DOWN, buff=0.1)
        
        # Arrange all content with proper spacing
        header_group = VGroup(slide4_title, separator)
        header_group.arrange(DOWN, buff=0.3)
        
        content_group = VGroup(header_group, values, final_group)
        content_group.arrange(DOWN, buff=0.7)
        content_group.move_to(content_area.get_center())
        
        # Animation
        self.play(
            Write(slide4_title),
            Create(separator),
            run_time=1
        )
        
        for value in values:
            self.play(
                FadeIn(value, shift=RIGHT*0.3),
                run_time=0.7
            )
        
        self.wait(0.5)
        self.play(
            Write(final_msg),
            Create(underline),
            run_time=0.8
        )
        
        self.wait(2)

if __name__ == "__main__":
    # Command to run: manim -pql camel_animation.py CamelStockComparison
    pass 