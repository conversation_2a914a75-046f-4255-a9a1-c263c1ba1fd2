from manim import *
import numpy as np
import random

class FluidCamelAnalysis(Scene):
    def construct(self):
        # Set sophisticated background
        self.camera.background_color = "#1a1a2e"
        
        # Create dynamic fluid background
        fluid_bg, particles, waves = self.create_fluid_background()
        self.add(fluid_bg, particles, waves)
        
        # Start continuous background animations
        self.start_fluid_animations(particles, waves)
        
        # Main animation sequence with smooth transitions based on storyboard
        self.title_sequence()
        self.ai_agent_introduction()
        self.data_comparison_sequence()
        self.value_and_future_sequence()
        
        # Clean up
        self.stop_fluid_animations(particles, waves)
    
    def create_fluid_background(self):
        # Gradient background with depth
        gradient = Rectangle(
            width=config.frame_width,
            height=config.frame_height,
            fill_opacity=1,
            stroke_width=0
        ).set_color_by_gradient(["#1a1a2e", "#16213e", "#0f3460"])
        
        # Floating particles with organic movement
        particles = VGroup()
        num_particles = 35  # Slightly reduced for better text visibility
        
        for i in range(num_particles):
            # Distribute particles avoiding center content area
            angle = random.uniform(0, TAU)
            distance = random.uniform(4.5, 7)  # Push particles further from center
            x = distance * np.cos(angle)
            y = distance * np.sin(angle)
            
            # Create organic particle shapes
            size = random.uniform(0.02, 0.06)  # Slightly smaller
            particle = Circle(
                radius=size,
                fill_opacity=random.uniform(0.3, 0.5),  # More subtle
                stroke_width=0,
                fill_color=random.choice(["#e94560", "#f38ba8", "#fab387", "#a6e3a1", "#74c7ec"])
            )
            particle.move_to([x, y, 0])
            
            # Add soft glow
            glow = Circle(
                radius=size * 2.5,  # Reduced glow
                fill_opacity=0.08,
                stroke_width=0,
                fill_color=particle.fill_color
            ).move_to(particle.get_center())
            
            particle_group = VGroup(glow, particle)
            particles.add(particle_group)
        
        # Flowing wave patterns
        waves = VGroup()
        num_waves = 5  # Reduced for cleaner look
        
        for i in range(num_waves):
            # Create flowing sine waves
            wave_points = []
            amplitude = random.uniform(0.4, 1.2)  # Reduced amplitude
            frequency = random.uniform(0.2, 0.6)
            phase = i * PI / 2.5
            
            for x in np.linspace(-8, 8, 40):
                y = amplitude * np.sin(frequency * x + phase) + random.uniform(-2.5, 2.5)
                wave_points.append([x, y, 0])
            
            wave = VMobject()
            wave.set_points_smoothly(wave_points)
            wave.set_stroke(
                width=1.2,
                opacity=0.25,  # More subtle
                color=random.choice(["#e94560", "#f38ba8", "#fab387"])
            )
            waves.add(wave)
        
        return gradient, particles, waves
    
    def start_fluid_animations(self, particles, waves):
        # Organic particle movement
        def float_particles(mob, dt):
            for particle_group in mob:
                particle = particle_group[1]
                glow = particle_group[0]
                
                current_pos = particle.get_center()
                
                # Organic floating motion with noise
                time = self.renderer.time
                noise_x = 0.4 * np.sin(time * 0.6 + current_pos[1] * 0.3)
                noise_y = 0.25 * np.cos(time * 0.4 + current_pos[0] * 0.4)
                
                new_x = current_pos[0] + noise_x * dt
                new_y = current_pos[1] + noise_y * dt
                
                # Boundary wrapping with smooth transition
                if abs(new_x) > 8:
                    new_x = -np.sign(new_x) * 7.5
                if abs(new_y) > 5:
                    new_y = -np.sign(new_y) * 4.5
                
                new_pos = [new_x, new_y, 0]
                particle.move_to(new_pos)
                glow.move_to(new_pos)
                
                # Breathing opacity
                base_opacity = 0.4
                breath = 0.15 * np.sin(time * 1.2 + current_pos[0] + current_pos[1])
                particle.set_fill(opacity=base_opacity + breath)
        
        # Wave flow animation
        def flow_waves(mob, dt):
            for i, wave in enumerate(mob):
                # Shift wave phase for flowing effect
                time = self.renderer.time
                phase_shift = time * 0.6 + i * PI / 2.5
                
                # Recreate wave with new phase
                wave_points = []
                amplitude = 0.8 + 0.2 * np.sin(time * 0.3 + i)
                frequency = 0.4
                
                for x in np.linspace(-8, 8, 40):
                    y = amplitude * np.sin(frequency * x + phase_shift) + (i - 2) * 0.9
                    wave_points.append([x, y, 0])
                
                wave.set_points_smoothly(wave_points)
                
                # Pulsing opacity
                opacity = 0.15 + 0.08 * np.sin(time * 1.8 + i)
                wave.set_stroke(opacity=opacity)
        
        particles.add_updater(float_particles)
        waves.add_updater(flow_waves)
    
    def stop_fluid_animations(self, particles, waves):
        particles.clear_updaters()
        waves.clear_updaters()
    
    def title_sequence(self):
        # Main title with enhanced visibility
        title = Text(
            "AI帮你选股？",
            font_size=84,
            color="#ffffff",
            stroke_width=3,
            stroke_color="#e94560"
        )
        
        # Subtitle with clear contrast
        subtitle = Text(
            "实测 CAMEL Agent 对比 AAPL vs NVDA",
            font_size=48,
            color="#fab387",
            stroke_width=2,
            stroke_color="#1a1a2e"
        )
        
        # Create content background for better readability
        content_bg = RoundedRectangle(
            width=12,
            height=4,
            corner_radius=0.3,
            fill_opacity=0.15,
            stroke_width=2,
            stroke_color="#74c7ec",
            stroke_opacity=0.6,
            fill_color="#1a1a2e"
        )
        
        # Arrange title elements
        title_group = VGroup(title, subtitle)
        title_group.arrange(DOWN, buff=0.8)
        
        # Position content background
        content_bg.surround(title_group, buff=0.8)
        
        # Ensure everything fits within boundaries
        full_group = VGroup(content_bg, title_group)
        if full_group.width > config.frame_width - 1:
            full_group.scale((config.frame_width - 1) / full_group.width)
        if full_group.height > config.frame_height - 1:
            full_group.scale((config.frame_height - 1) / full_group.height)
        
        # Animation sequence
        self.play(
            FadeIn(content_bg, scale=0.8),
            run_time=1
        )
        
        # Title emerges character by character
        for i, char in enumerate(title):
            char.set_fill(opacity=0)
            char.scale(0.5)
            self.play(
                char.animate.scale(2).set_fill(opacity=1),
                run_time=0.15,
                rate_func=rush_from
            )
        
        self.play(
            Write(subtitle),
            run_time=1.5
        )
        
        self.wait(2)
        
        # Smooth transition out
        self.play(
            FadeOut(full_group, shift=UP),
            run_time=1.5
        )
    
    def ai_agent_introduction(self):
        # Section title
        section_title = Text(
            "AI Agent 股票分析：从零到有",
            font_size=56,
            color="#74c7ec",
            stroke_width=2,
            stroke_color="#1a1a2e"
        ).to_edge(UP, buff=1.2)
        
        # Key points with enhanced visibility
        key_points = [
            "CAMEL：专业股票分析师AI框架",
            "自动获取实时股票行情与财务指标", 
            "对比评估苹果(AAPL)与英伟达(NVDA)"
        ]
        
        # Create content cards for each point
        point_cards = VGroup()
        colors = ["#e94560", "#f38ba8", "#a6e3a1"]
        
        for i, (point, color) in enumerate(zip(key_points, colors)):
            # Background card
            card_bg = RoundedRectangle(
                width=10,
                height=1.2,
                corner_radius=0.2,
                fill_opacity=0.2,
                stroke_width=2,
                stroke_color=color,
                stroke_opacity=0.8,
                fill_color=color
            )
            
            # Point text
            point_text = Text(
                point,
                font_size=36,
                color="#ffffff",
                stroke_width=1,
                stroke_color="#1a1a2e"
            )
            
            # Icon
            icon = Circle(
                radius=0.25,
                fill_opacity=0.8,
                stroke_width=2,
                stroke_color="#ffffff",
                fill_color=color
            )
            
            # Arrange elements
            content = VGroup(icon, point_text)
            content.arrange(RIGHT, buff=0.5)
            content.move_to(card_bg.get_center())
            
            card = VGroup(card_bg, content)
            point_cards.add(card)
        
        # Position cards
        point_cards.arrange(DOWN, buff=0.6)
        point_cards.move_to(DOWN * 0.5)
        
        # Ensure within boundaries
        content_group = VGroup(section_title, point_cards)
        if content_group.width > config.frame_width - 1:
            content_group.scale((config.frame_width - 1) / content_group.width)
        if content_group.height > config.frame_height - 1:
            content_group.scale((config.frame_height - 1) / content_group.height)
        
        # Animation
        self.play(
            Write(section_title),
            run_time=1.5
        )
        
        # Cards appear with stagger effect
        for i, card in enumerate(point_cards):
            self.play(
                FadeIn(card, shift=0.5*RIGHT),
                run_time=0.8
            )
            self.wait(0.3)
        
        self.wait(2)
        
        self.play(
            FadeOut(content_group),
            run_time=1.5
        )
    
    def data_comparison_sequence(self):
        # Section title
        section_title = Text(
            "核心数据对比",
            font_size=56,
            color="#e94560",
            stroke_width=2,
            stroke_color="#1a1a2e"
        ).to_edge(UP, buff=1)
        
        # Stock symbols with enhanced design
        aapl_card = self.create_stock_card("AAPL", "苹果公司", "#f38ba8", LEFT * 3)
        nvda_card = self.create_stock_card("NVDA", "英伟达", "#74c7ec", RIGHT * 3)
        
        vs_text = Text(
            "VS",
            font_size=64,
            color="#ffffff",
            stroke_width=3,
            stroke_color="#fab387"
        )
        
        # Key metrics comparison
        metrics_data = [
            ("最新股价", "$210.20", "$134.98"),
            ("市值", "3.50万亿", "2.91万亿"),
            ("市盈率", "37.29", "39.90")
        ]
        
        metrics_table = self.create_metrics_table(metrics_data)
        
        # Animation sequence
        self.play(Write(section_title), run_time=1)
        
        # Stock cards appear
        self.play(
            FadeIn(aapl_card, shift=0.5*RIGHT),
            FadeIn(nvda_card, shift=0.5*LEFT),
            run_time=1.2
        )
        
        self.play(
            Write(vs_text),
            run_time=0.8
        )
        
        # Metrics table appears
        self.play(
            FadeIn(metrics_table, shift=UP),
            run_time=1.5
        )
        
        self.wait(3)
        
        self.play(
            FadeOut(VGroup(section_title, aapl_card, nvda_card, vs_text, metrics_table)),
            run_time=1.5
        )
    
    def create_stock_card(self, symbol, name, color, position):
        # Card background
        card_bg = RoundedRectangle(
            width=3,
            height=2,
            corner_radius=0.3,
            fill_opacity=0.25,
            stroke_width=3,
            stroke_color=color,
            stroke_opacity=0.9,
            fill_color=color
        )
        
        # Symbol text
        symbol_text = Text(
            symbol,
            font_size=48,
            color="#ffffff",
            stroke_width=2,
            stroke_color="#1a1a2e"
        )
        
        # Company name
        name_text = Text(
            name,
            font_size=24,
            color=color,
            stroke_width=1,
            stroke_color="#1a1a2e"
        )
        
        # Arrange content
        content = VGroup(symbol_text, name_text)
        content.arrange(DOWN, buff=0.3)
        content.move_to(card_bg.get_center())
        
        card = VGroup(card_bg, content)
        card.move_to(position)
        
        return card
    
    def create_metrics_table(self, data):
        table = VGroup()
        
        # Table background
        table_bg = RoundedRectangle(
            width=10,
            height=4,
            corner_radius=0.3,
            fill_opacity=0.15,
            stroke_width=2,
            stroke_color="#fab387",
            stroke_opacity=0.7,
            fill_color="#1a1a2e"
        )
        
        # Create rows
        rows = VGroup()
        for i, (metric, aapl_val, nvda_val) in enumerate(data):
            # Metric label
            metric_text = Text(
                metric,
                font_size=32,
                color="#a6e3a1",
                stroke_width=1,
                stroke_color="#1a1a2e"
            )
            
            # AAPL value
            aapl_text = Text(
                aapl_val,
                font_size=28,
                color="#f38ba8",
                stroke_width=1,
                stroke_color="#1a1a2e"
            )
            
            # NVDA value
            nvda_text = Text(
                nvda_val,
                font_size=28,
                color="#74c7ec",
                stroke_width=1,
                stroke_color="#1a1a2e"
            )
            
            # Arrange row
            row = VGroup(metric_text, aapl_text, nvda_text)
            row.arrange(RIGHT, buff=1.5)
            rows.add(row)
        
        rows.arrange(DOWN, buff=0.6)
        rows.move_to(table_bg.get_center())
        
        table.add(table_bg, rows)
        table.move_to(DOWN * 1.5)
        
        return table
    
    def value_and_future_sequence(self):
        # Section title
        section_title = Text(
            "AI Agent 的价值与未来",
            font_size=56,
            color="#fab387",
            stroke_width=2,
            stroke_color="#1a1a2e"
        ).to_edge(UP, buff=1)
        
        # Value points
        value_points = [
            "自动化数据收集与整理",
            "快速生成对比分析", 
            "提供基于数据的初步洞察"
        ]
        
        # Create value cards
        value_cards = VGroup()
        colors = ["#e94560", "#74c7ec", "#a6e3a1"]
        icons = ["⚙️", "⚡", "💡"]
        
        for i, (point, color, icon) in enumerate(zip(value_points, colors, icons)):
            card = self.create_value_card(point, color, icon)
            value_cards.add(card)
        
        value_cards.arrange(DOWN, buff=0.8)
        value_cards.move_to(DOWN * 0.5)
        
        # Conclusion text
        conclusion = Text(
            "AI赋能金融投资决策",
            font_size=48,
            color="#ffffff",
            stroke_width=3,
            stroke_color="#e94560"
        ).move_to(DOWN * 3)
        
        # Conclusion background
        conclusion_bg = RoundedRectangle(
            width=conclusion.width + 1,
            height=conclusion.height + 0.5,
            corner_radius=0.3,
            fill_opacity=0.2,
            stroke_width=2,
            stroke_color="#e94560",
            stroke_opacity=0.8,
            fill_color="#e94560"
        ).move_to(conclusion.get_center())
        
        # Animation
        self.play(Write(section_title), run_time=1)
        
        # Value cards appear
        for card in value_cards:
            self.play(
                FadeIn(card, shift=0.3*UP),
                run_time=0.8
            )
        
        # Conclusion appears
        self.play(
            FadeIn(conclusion_bg),
            Write(conclusion),
            run_time=1.5
        )
        
        self.wait(3)
        
        # Final fade out
        self.play(
            FadeOut(VGroup(section_title, value_cards, conclusion_bg, conclusion)),
            run_time=2
        )
    
    def create_value_card(self, text, color, icon):
        # Card background
        card_bg = RoundedRectangle(
            width=9,
            height=1.2,
            corner_radius=0.3,
            fill_opacity=0.2,
            stroke_width=2,
            stroke_color=color,
            stroke_opacity=0.8,
            fill_color=color
        )
        
        # Icon
        icon_text = Text(
            icon,
            font_size=36
        )
        
        # Main text
        main_text = Text(
            text,
            font_size=32,
            color="#ffffff",
            stroke_width=1,
            stroke_color="#1a1a2e"
        )
        
        # Arrange content
        content = VGroup(icon_text, main_text)
        content.arrange(RIGHT, buff=0.5)
        content.move_to(card_bg.get_center())
        
        card = VGroup(card_bg, content)
        return card

if __name__ == "__main__":
    # Command to run: manim -pql fluid_camel_animation.py FluidCamelAnalysis
    pass 