from docling_core.experimental.serializer.base import BaseDoc<PERSON>erializer, BaseTableSerializer, SerializationResult
from docling_core.experimental.serializer.markdown import (
    MarkdownDocSerializer,
    MarkdownListSerializer,
    MarkdownTextSerializer,
)
from docling_core.types.doc import (
    DoclingDocument,
    ImageRefMode,
    TableItem,
)
from tabulate import tabulate
from typing_extensions import override


class MarkdownTableSerializer(BaseTableSerializer):
    """Markdown-specific table item serializer."""

    @override
    def serialize(
        self,
        *,
        item: TableItem,
        doc_serializer: BaseDocSerializer,
        doc: DoclingDocument,
        **kwargs,
    ) -> SerializationResult:
        """Serializes the passed item."""
        text_parts: list[str] = []

        if caption_txt := doc_serializer.serialize_captions(
            item=item,
        ).text:
            text_parts.append(caption_txt)

        # Include table image if it exists and has a URI
        # The URI is set externally, e.g., by _save_image_path_and_caption
        if item.image is not None and item.image.uri:
            # Consider using a more descriptive alt text
            text_res = f"![Table Image]({str(item.image.uri)})"
            text_parts.append(text_res)

        if item.self_ref not in doc_serializer.get_excluded_refs():
            rows = [
                [
                    # make sure that md tables are not broken
                    # due to newline chars in the text
                    col.text.replace("\n", " ")
                    for col in row
                ]
                for row in item.data.grid
            ]
            if len(rows) > 1 and len(rows[0]) > 0:
                try:
                    table_text = tabulate(rows[1:], headers=rows[0], tablefmt="github")
                except ValueError:
                    table_text = tabulate(
                        rows[1:],
                        headers=rows[0],
                        tablefmt="github",
                        disable_numparse=True,
                    )
            else:
                table_text = ""
            if table_text:
                text_parts.append(table_text)

        text_res = "\n\n".join(text_parts)

        return SerializationResult(text=text_res)


def export_to_markdown(document: DoclingDocument, image_mode: ImageRefMode) -> str:
    """Exports a DoclingDocument to a Markdown string using custom serializers."""
    serializer = MarkdownDocSerializer(
        doc=document,
        image_mode=image_mode,
        pages=None,
        escaping_underscores=True,
        text_serializer=MarkdownTextSerializer(
            wrap_width=None,
        ),
        list_serializer=MarkdownListSerializer(
            indent=4,
        ),
    )
    # Use the custom table serializer that includes images
    serializer.table_serializer = MarkdownTableSerializer()
    ser_res = serializer.serialize()

    return ser_res.text
